[
  {
    "_1": 2,
    "_361": 362
  },
  "root",
  {
    "_3": 4
  },
  "data",
  {
    "_5": -5,
    "_6": 7,
    "_51": 52,
    "_97": -7,
    "_98": 99,
    "_330": -7,
    "_331": 332
  },
  "theme",
  "auth",
  {
    "_8": 9,
    "_10": 11,
    "_12": 11,
    "_13": 14,
    "_15": 16,
    "_17": 18,
    "_19": 20,
    "_21": 16,
    "_22": 23,
    "_24": 25,
    "_29": 30,
    "_36": 37,
    "_45": 48,
    "_47": 49,
    "_50": 34
  },
  "id",
  "53b22037-02e4-4458-bbab-3ff4b6280a46",
  "aud",
  "authenticated",
  "role",
  "email",
  "<EMAIL>",
  "email_confirmed_at",
  "2025-06-08T12:00:08.079239Z",
  "phone",
  "",
  "confirmation_sent_at",
  "2025-06-08T11:59:31.100389Z",
  "confirmed_at",
  "last_sign_in_at",
  "2025-06-08T17:30:21.151769Z",
  "app_metadata",
  {
    "_26": 13,
    "_27": 28
  },
  "provider",
  "providers",
  [
    13
  ],
  "user_metadata",
  {
    "_13": 14,
    "_31": 32,
    "_33": 34,
    "_35": 9
  },
  "email_verified",
  true,
  "phone_verified",
  false,
  "sub",
  "identities",
  [
    38
  ],
  {
    "_39": 40,
    "_8": 9,
    "_41": 9,
    "_42": 43,
    "_26": 13,
    "_22": 44,
    "_45": 46,
    "_47": 46,
    "_13": 14
  },
  "identity_id",
  "598c50a4-ed7f-4005-8b81-0d57a1a17d23",
  "user_id",
  "identity_data",
  {
    "_13": 14,
    "_31": 32,
    "_33": 34,
    "_35": 9
  },
  "2025-06-08T11:59:31.090037Z",
  "created_at",
  "2025-06-08T11:59:31.090089Z",
  "updated_at",
  "2025-06-08T11:59:31.076622Z",
  "2025-06-08T17:30:21.157731Z",
  "is_anonymous",
  "user",
  {
    "_8": 53,
    "_54": 55,
    "_56": 57,
    "_58": 9,
    "_12": 59,
    "_13": 14,
    "_60": -5,
    "_61": -5,
    "_62": 63,
    "_64": 65,
    "_66": -5,
    "_67": -5,
    "_68": -5,
    "_69": -5,
    "_70": -5,
    "_71": -5,
    "_72": -5,
    "_73": -5,
    "_74": 34,
    "_75": -5,
    "_76": 77,
    "_78": -5,
    "_79": 80,
    "_81": 82,
    "_83": -5,
    "_84": 34,
    "_85": -5,
    "_86": -5,
    "_87": -5,
    "_88": -5,
    "_89": -5,
    "_90": -5,
    "_91": -5,
    "_92": -5,
    "_93": -5,
    "_94": 34,
    "_95": -5,
    "_96": -5
  },
  "cmbnm3etk0tyn9a0w4axfitir",
  "createdAt",
  [
    "D",
    1749384048585
  ],
  "updatedAt",
  [
    "D",
    1749384048585
  ],
  "authId",
  "User",
  "walletAddress",
  "previewWalletAddress",
  "username",
  "kiechck",
  "name",
  "kk",
  "avatarId",
  "bannerId",
  "biography",
  "githubHandle",
  "twitterHandle",
  "discordHandle",
  "telegramHandle",
  "websiteHandle",
  "isCreator",
  "dynamicaId",
  "updatedUsernameAt",
  [
    "D",
    1749384048228
  ],
  "stakingTermsConsentAt",
  "termsConsentAt",
  [
    "D",
    1749384048228
  ],
  "dateOfBirth",
  [
    "D",
    624153600000
  ],
  "chainId",
  "betaAccess",
  "trialEndsAt",
  "stripeCustomerId",
  "stripeSubscriptionId",
  "subscriptionStatus",
  "subscriptionStartDate",
  "subscriptionCancelAt",
  "subscriptionCancelledAt",
  "lastTopUpAt",
  "planId",
  "isBanned",
  "avatar",
  "banner",
  "toast",
  "translations",
  {
    "_100": 100,
    "_101": 102,
    "_103": 103,
    "_104": 105,
    "_106": 106,
    "_107": 108,
    "_109": 110,
    "_111": 112,
    "_116": 117,
    "_142": 143,
    "_211": 212,
    "_282": 283
  },
  "Email",
  "Greet {{name}}",
  "Hello, {{name}}!",
  "Password",
  "Password Repeat",
  "Password (repeat)",
  "Login",
  "Logout",
  "Log out",
  "Sign Up",
  "Sign up",
  "Enum",
  {
    "_113": 114
  },
  "Role",
  {
    "_59": 59,
    "_115": 115
  },
  "Admin",
  "ProviderOptions",
  {
    "_118": 119,
    "_128": 129
  },
  "capability",
  {
    "_120": 121,
    "_122": 123,
    "_124": 125,
    "_126": 127
  },
  "textGeneration",
  "Text generation",
  "imageGeneration",
  "Image generation",
  "speechToText",
  "Speech to Text",
  "audio",
  "Audio",
  "imageSize",
  {
    "_130": 131,
    "_132": 133,
    "_134": 135,
    "_136": 137,
    "_138": 139,
    "_140": 141
  },
  "square",
  "Square",
  "square_hd",
  "Square (HD)",
  "portrait_4_3",
  "Portrait (4:3)",
  "portrait_16_9",
  "Portrait (16:9)",
  "landscape_4_3",
  "Landscape (4:3)",
  "landscape_16_9",
  "Landscape (16:9)",
  "Error",
  {
    "_144": 145,
    "_59": 154,
    "_172": 173,
    "_178": 179,
    "_186": 187,
    "_196": 197,
    "_204": 205,
    "_208": 209
  },
  "SupabaseAuth",
  {
    "_146": 147,
    "_148": 149,
    "_150": 151,
    "_152": 153
  },
  "invalid_credentials",
  "The credentials provided are incorrect.",
  "email_not_confirmed",
  "Please confirm your email address first.",
  "same_password",
  "Your new password should be different from your old password.",
  "otp_expired",
  "Your token has expired. Please try again with a new token.",
  {
    "_155": 156,
    "_157": 158,
    "_159": 160,
    "_161": 162,
    "_163": 164,
    "_165": 156,
    "_166": 167,
    "_168": 169,
    "_170": 171
  },
  "NO_USER_FOR_AUTH_ID",
  "User not found.",
  "ALREADY_ONBOARDED",
  "You have been onboarded already.",
  "USER_ALREADY_EXISTS",
  "User already exists.",
  "REQUIRES_ONBOARDING",
  "Please onboard your account.",
  "USERNAME_IN_USE",
  "This username is not available.",
  "NOT_FOUND",
  "MUST_BE_18",
  "You must be at least 18 years old to use our platform.",
  "INVALID_DATE",
  "The date you provided is invalid.",
  "DB_ERROR",
  "Database error.",
  "BadRequest",
  {
    "_174": 175,
    "_176": 177
  },
  "INVALID_REQUEST",
  "Request is invalid.",
  "NOT_IMPLEMENTED",
  "This feature is not implemented yet.",
  "Internal",
  {
    "_180": 181,
    "_182": 183,
    "_184": 185
  },
  "UNEXPECTED",
  "Something went wrong, try again later.",
  "NO_AUTH_USER_ID_AFTER_SIGN_UP",
  "No auth user was returned after sign up.",
  "NO_AUTH_SESSION_AFTER_SIGN_UP",
  "No auth session was returned after sign up.",
  "Prompt",
  {
    "_188": 189,
    "_190": 191,
    "_192": 193,
    "_194": 195
  },
  "QUERY_RESPONSE_NOT_PENDING",
  "Trying to perform a prompt for a query that is not pending.",
  "QUERY_REQUEST_INVALID_FORMAT",
  "Prompt request invalid format.",
  "QUERY_NOT_FOUND",
  "Query not found.",
  "QUERY_REQUEST_MISSING_INPUT_FIELDS",
  "Query missing input.",
  "Authentication",
  {
    "_198": 199,
    "_200": 201,
    "_202": 203
  },
  "NOT_AUTHENTICATED",
  "Please log in or sign up to continue.",
  "ETHER_VERIFY_MESSAGE_ERROR",
  "Ether verify message went wrong.",
  "RESET_PASSWORD_MIGRATION",
  "It looks like you had a Beta account.\nPlease reset your password via the forgot password page to regain access.",
  "Authorization",
  {
    "_206": 207
  },
  "NOT_AUTHORIZED",
  "You are not authorized for this.",
  "Creator",
  {
    "_165": 210
  },
  "Creator not found.",
  "Form",
  {
    "_213": 214,
    "_215": 216,
    "_217": 218,
    "_219": 220,
    "_221": 222,
    "_223": 224,
    "_225": 226,
    "_227": 220,
    "_228": 229,
    "_230": 231,
    "_232": 233,
    "_234": 235,
    "_236": 237,
    "_238": 239,
    "_240": 241,
    "_242": 243,
    "_244": 245,
    "_246": 247,
    "_248": 249,
    "_250": 251,
    "_252": 253,
    "_254": 255,
    "_256": 257,
    "_258": 259,
    "_260": 261,
    "_262": 263,
    "_264": 265,
    "_266": 267,
    "_268": 269,
    "_270": 271,
    "_272": 273,
    "_274": 275,
    "_276": 277,
    "_278": 279,
    "_280": 281
  },
  "PASSWORDS_DO_NOT_MATCH",
  "Passwords do not match.",
  "REQUIRED",
  "Field is required.",
  "EMAIL_REQUIRED",
  "Email address is required.",
  "EMAIL_INVALID",
  "Email address is invalid.",
  "NO_ACCOUNT_FOUND",
  "No account found with this email address.",
  "PASSWORD_REQUIRED",
  "Password is required.",
  "PASSWORD_INCORRECT",
  "Password is incorrect.",
  "INVALID_EMAIL",
  "EMAIL_TOO_LONG",
  "Email address is too long.",
  "SIGNUP_FAILED",
  "Sign up failed.",
  "PASSWORD_MIN_8_CHARACTERS",
  "Must be 8+ characters",
  "PASSWORD_REQUIRES_UPPERCASE",
  "Must have uppercase letter",
  "PASSWORD_REQUIRES_LOWERCASE",
  "Must have lowercase letter",
  "PASSWORD_REQUIRES_NUMBER",
  "Must have number",
  "PASSWORD_REQUIRES_SPECIAL",
  "Must have special character",
  "USERNAME_MIN_3_CHARACTERS",
  "Must be 3+ characters",
  "USERNAME_MAX_16_CHARACTERS",
  "Must be under 16 characters",
  "USERNAME_ALPHANUMERIC",
  "Use only letters and numbers.",
  "USERNAME_ALPHABETIC",
  "Must contain a letter.",
  "NAME_ALPHABETIC_ONLY",
  "Use only letters.",
  "NAME_MAX_20_CHARACTERS",
  "Max 20 characters.",
  "FILE_TOO_LARGE",
  "File is too large.",
  "FILE_READ_ERROR",
  "Error reading file.",
  "FILE_INVALID_TYPE",
  "File type is invalid.",
  "PROMPT_REQUIRED",
  "Please include a prompt.",
  "IMAGE_REQUIRED",
  "Attach an image first.",
  "AUDIO_REQUIRED",
  "Attach an audio file first.",
  "INVALID_URL",
  "URL is invalid.",
  "SLUG_INVALID",
  "Slug is invalid.",
  "DATE_OF_BIRTH_INVALID",
  "Use DD/MM/YYYY format",
  "TERMS_CONSENT_REQUIRED",
  "You must accept the Terms of Service.",
  "EXPECTED_NUMBER",
  "Enter a number.",
  "MIN_1_TOKEN_STAKE",
  "Stake atleast one ticket.",
  "MIN_1_TOKEN_UNSTAKE",
  "Unstake atleast one ticket.",
  "INSUFFICIENT_BALANCE",
  "You don't have enough balance for this.",
  "Success",
  {
    "_284": 285,
    "_290": 291,
    "_294": 295,
    "_298": 299,
    "_302": 303,
    "_306": 307,
    "_310": 311,
    "_314": 315,
    "_318": 319,
    "_322": 323,
    "_326": 327
  },
  "ResetPassword",
  {
    "_286": 287,
    "_288": 289
  },
  "Title",
  "Password Reset Successful",
  "Description",
  "Your password has been updated.",
  "ResetPasswordRequest",
  {
    "_286": 292,
    "_288": 293
  },
  "Password Reset Request Succesful",
  "Check your mailbox for instructions to reset your password.",
  "Register",
  {
    "_286": 296,
    "_288": 297
  },
  "Sign Up Successful",
  "Check your mailbox to confirm your email address.",
  "OnboardingCompleted",
  {
    "_286": 300,
    "_288": 301
  },
  "Onboarding Successful",
  "You're set and done.",
  "ProfileUpdated",
  {
    "_286": 304,
    "_288": 305
  },
  "Profile Updated",
  "Your profile has been updated.",
  "StakingTermsAccepted",
  {
    "_286": 308,
    "_288": 309
  },
  "Staking Terms of Service Accepted",
  "You've accepted the staking Terms of Service.",
  "QueryHistoryCleared",
  {
    "_286": 312,
    "_288": 313
  },
  "Chat History Cleared",
  "Your history has been cleared.",
  "CreatedModel",
  {
    "_286": 316,
    "_288": 317
  },
  "Model Created",
  "Model is created.",
  "UpdatedModel",
  {
    "_286": 320,
    "_288": 321
  },
  "Model Updated",
  "Model has been updated.",
  "ManagedModel",
  {
    "_286": 324,
    "_288": 325
  },
  "Model Managed",
  "Model has been managed.",
  "UpdatedUser",
  {
    "_286": 328,
    "_288": 329
  },
  "User Updated",
  "User has been updated.",
  "wagmi",
  "ENV",
  {
    "_333": 334,
    "_335": 336,
    "_337": 338,
    "_339": 340,
    "_341": 342,
    "_343": 344,
    "_345": -7,
    "_346": 347,
    "_348": 349,
    "_350": 351,
    "_352": 353,
    "_354": 355,
    "_356": -7,
    "_357": 358,
    "_359": 360
  },
  "SUPABASE_ANON_KEY",
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBwZGpsbWFqbXBjcXBrZG1uemZkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQxNzgwNjUsImV4cCI6MjA0OTc1NDA2NX0.mxwm-Ews4gqtFOzakIN9M-A6Syb0zVx6TivCfUddKLM",
  "APP_URL",
  "https://app.verticalstudio.ai",
  "GOOGLE_OAUTH_CLIENT_ID",
  "1082725546080-cpceu8ggj47tsp7qifu06oh4vqdklepk.apps.googleusercontent.com",
  "GOOGLE_TAG_MANAGER_ID",
  "GTM-MJ4BF3V5",
  "SUPABASE_URL",
  "https://ppdjlmajmpcqpkdmnzfd.supabase.co",
  "VERTICAL_API_URL",
  "https://api.verticalstudio.ai",
  "MOCK_FINETUNING",
  "NODE_ENV",
  "production",
  "MIXPANEL_TOKEN",
  "b4c6bc9066d61f2c6dae09cb3366be67",
  "CLARITY_PROJECT_ID",
  "q96sq83p2v",
  "META_PIXEL_ID",
  "625879130382245",
  "JSON_RPC_PROVIDER_URL",
  "https://bold-tiniest-tent.quiknode.pro/fe1f30aadf41043301f6057785eb430dde2b8f5f",
  "INFURA_ID",
  "CONTRACT_ADDRESS",
  "0x8dc4bfce6b547fafb4d026d88c2171b2c2982b9a",
  "HCAPTCHA_SITE_KEY",
  "70c26416-e52e-4348-94a8-84241ed6042c",
  "routes/stream._index",
  {
    "_3": 363
  },
  {
    "_364": 365
  },
  "models": [
    {
      "id": "gpt-4o-mini",
      "title": "GPT-4o Mini",
      "description": "A smaller, more affordable version of GPT-4o, GPT-4o Mini offers a balance of performance and cost-efficiency for various AI applications. It excels in text and vision tasks with extensive context support.",
      "slug": "gpt-4o-mini",
      "image": "/images/models/image-24.webp",
      "categories": ["Text Generation", "Image Analysis", "Web Search"],
      "tags": ["Smallest", "Fastest", "OpenAI", "Text generation"],
      "input": ["text"],
      "output": ["text"],
      "supportsNativeWebSearch": true,
      "bestFor": ["Day-to-day chatting", "Q&A", "Drafting emails and content", "Lightweight coding help"],
      "strengths": ["Fast responses", "Solid reasoning ability", "Low usage cost", "Image understanding"],
      "useCases": ["Casual conversation", "Quick assistance", "Basic coding support", "Image description"],
      "releaseDate": "2024",
      "welcome": {
        "title": "Meet GPT-4o Mini: Your expert assistant.",
        "messages": ["Ask me anything complex.", "Need a legal analysis?", "Want detailed code help?"]
      },
      "type": "text"
    },
  {
    "_8": 367,
    "_368": 369,
    "_370": 371,
    "_372": 367,
    "_373": 374,
    "_375": 376,
    "_380": 381,
    "_51": -5,
    "_385": 386,
    "_388": 389,
    "_390": 391,
    "_401": 32,
    "_402": 403,
    "_408": 409,
    "_414": 415,
    "_420": 421,
    "_422": 423,
    "_26": 384,
    "_430": 387
  },
  "gpt-4o-mini",
  "title",
  "GPT-4o Mini",
  "description",
  "A smaller, more affordable version of GPT-4o, GPT-4o Mini offers a balance of performance and cost-efficiency for various AI applications. It excels in text and vision tasks with extensive context support.",
  "slug",
  "image",
  "/images/models/image-24.webp",
  "categories",
  [
    377,
    378,
    379
  ],
  "Text Generation",
  "Image Analysis",
  "Web Search",
  "tags",
  [
    382,
    383,
    384,
    121
  ],
  "Smallest",
  "Fastest",
  "OpenAI",
  "input",
  [
    387
  ],
  "text",
  "output",
  [
    387
  ],
  "options",
  {
    "_387": 392
  },
  {
    "_393": 394
  },
  "file",
  {
    "_395": 396
  },
  "mimeType",
  {
    "_397": 398
  },
  "image/*",
  [
    399,
    400
  ],
  ".png",
  ".jpg",
  "supportsNativeWebSearch",
  "bestFor",
  [
    404,
    405,
    406,
    407
  ],
  "Day-to-day chatting",
  "Q&A",
  "Drafting emails and content",
  "Lightweight coding help",
  "strengths",
  [
    410,
    411,
    412,
    413
  ],
  "Fast responses",
  "Solid reasoning ability",
  "Low usage cost",
  "Image understanding",
  "useCases",
  [
    416,
    417,
    418,
    419
  ],
  "Casual conversation",
  "Quick assistance",
  "Basic coding support",
  "Image description",
  "releaseDate",
  "2024",
  "welcome",
  {
    "_368": 424,
    "_425": 426
  },
  "Meet GPT-4o Mini: Your expert assistant.",
  "messages",
  [
    427,
    428,
    429
  ],
  "Ask me anything complex.",
  "Need a legal analysis?",
  "Want detailed code help?",
  "type",
  {
    "_8": 432,
    "_368": 433,
    "_370": 434,
    "_372": 435,
    "_373": 436,
    "_375": 437,
    "_380": 439,
    "_51": 445,
    "_385": 446,
    "_388": 447,
    "_390": 448,
    "_402": 460,
    "_408": 465,
    "_414": 470,
    "_420": 475,
    "_26": 445,
    "_430": 387
  },
  "claude-4-opus-20250514",
  "Claude 4 Opus",
  "Claude 4 Opus is Anthropic's most powerful model and the world's best coding model, leading on SWE-bench (72.5%) and Terminal-bench (43.2%). It delivers sustained performance on long-running tasks that require focused effort and thousands of steps, with the ability to work continuously for several hours—dramatically outperforming all Sonnet models and significantly expanding what AI agents can accomplish.",
  "claude-4-opus",
  "/images/models/image-33.webp",
  [
    377,
    378,
    438
  ],
  "PDF Analysis",
  [
    440,
    441,
    442,
    443,
    444
  ],
  "Intelligent",
  "Complex tasks",
  "Research",
  "LLM",
  "Coding",
  "Anthropic",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 449
  },
  {
    "_450": 451,
    "_393": 454
  },
  "reasoning",
  {
    "_452": 453
  },
  "defaultValue",
  "on",
  {
    "_395": 455
  },
  {
    "_456": 457,
    "_397": 459
  },
  "application/pdf",
  [
    458
  ],
  ".pdf",
  [
    400,
    399
  ],
  [
    461,
    462,
    463,
    464
  ],
  "Complex coding tasks",
  "Extended dialogues",
  "Strategic analysis",
  "Research and discovery",
  [
    466,
    467,
    468,
    469
  ],
  "World-leading coding capabilities",
  "Sustained performance on long tasks",
  "Enhanced memory capabilities",
  "Superior reasoning",
  [
    471,
    472,
    473,
    474
  ],
  "Software development",
  "Complex problem-solving",
  "Research analysis",
  "Agent workflows",
  "2025-05-14",
  {
    "_8": 477,
    "_368": 478,
    "_370": 479,
    "_372": 480,
    "_373": 481,
    "_375": 482,
    "_380": 483,
    "_51": 445,
    "_385": 485,
    "_388": 486,
    "_390": 487,
    "_402": 494,
    "_408": 497,
    "_414": 502,
    "_420": 475,
    "_26": 445,
    "_430": 387
  },
  "claude-4-sonnet-20250514",
  "Claude 4 Sonnet",
  "Claude 4 Sonnet significantly improves on Sonnet 3.7's industry-leading capabilities, excelling in coding with a state-of-the-art 72.7% on SWE-bench. The model balances performance and efficiency for internal and external use cases, with enhanced steerability for greater control over implementations. While not matching Opus 4 in most domains, it delivers an optimal mix of capability and practicality.",
  "claude-4-sonnet",
  "/images/models/image-32.webp",
  [
    377,
    378,
    438
  ],
  [
    484,
    444,
    443
  ],
  "Reasoning",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 488
  },
  {
    "_450": 489,
    "_393": 490
  },
  {
    "_452": 453
  },
  {
    "_395": 491
  },
  {
    "_456": 492,
    "_397": 493
  },
  [
    458
  ],
  [
    400,
    399
  ],
  [
    471,
    472,
    495,
    496
  ],
  "Long-form content creation",
  "Technical analysis",
  [
    498,
    499,
    500,
    501
  ],
  "State-of-the-art coding performance",
  "Enhanced instruction following",
  "Improved problem-solving",
  "Balanced capabilities",
  [
    503,
    504,
    505,
    506
  ],
  "Application development",
  "Code review and debugging",
  "Technical documentation",
  "Complex analysis",
  {
    "_8": 508,
    "_368": 509,
    "_370": 510,
    "_372": 511,
    "_373": 512,
    "_375": 513,
    "_380": 514,
    "_51": 445,
    "_385": 515,
    "_388": 516,
    "_390": 517,
    "_402": 524,
    "_408": 528,
    "_414": 533,
    "_420": 537,
    "_26": 445,
    "_430": 387
  },
  "claude-3-7-sonnet-20250219",
  "Claude 3.7 Sonnet",
  "Claude 3.7 Sonnet is Anthropic's most advanced hybrid reasoning model, excelling at real-world coding, deep reasoning, and complex multi-step tasks. It supports fast responses or extended thinking, making it ideal for software development, structured writing, and technical problem-solving.",
  "claude-3-7-sonnet",
  "/images/models/image-2.webp",
  [
    377,
    378,
    438
  ],
  [
    484,
    444,
    443
  ],
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 518
  },
  {
    "_450": 519,
    "_393": 520
  },
  {
    "_452": 453
  },
  {
    "_395": 521
  },
  {
    "_456": 522,
    "_397": 523
  },
  [
    458
  ],
  [
    400,
    399
  ],
  [
    525,
    526,
    495,
    527
  ],
  "In-depth conversations",
  "Coding help",
  "Thorough analysis",
  [
    529,
    530,
    531,
    532
  ],
  "High accuracy and coherence",
  "Large context window",
  "Polite and helpful tone",
  "Excellent at coding",
  [
    534,
    535,
    472,
    536
  ],
  "Drafting reports",
  "Debugging code",
  "Detailed analysis",
  "2025-02-19",
  {
    "_8": 539,
    "_368": 540,
    "_370": 541,
    "_372": 542,
    "_373": 543,
    "_375": 544,
    "_380": 545,
    "_51": 445,
    "_385": 547,
    "_388": 548,
    "_390": 549,
    "_402": 551,
    "_408": 556,
    "_414": 561,
    "_420": 566,
    "_26": 445,
    "_430": 387
  },
  "claude-3-5-haiku-20241022",
  "Claude 3.5 Haiku",
  "Claude 3.5 Haiku is Anthropic's fastest model, combining speed with strong performance in coding, instruction following, and real-time tasks. Ideal for chatbots, content moderation, and sub-agent workflows, it delivers high accuracy at a low cost, even outperforming larger models on many benchmarks.",
  "claude-3-5-haiku",
  "/images/models/image-3.webp",
  [
    377
  ],
  [
    546,
    444,
    443
  ],
  "Fast",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 550
  },
  {},
  [
    552,
    553,
    554,
    555
  ],
  "General-purpose chatting",
  "Creative writing",
  "Summaries",
  "Moderate-length tasks",
  [
    557,
    558,
    559,
    560
  ],
  "Fast and responsive",
  "Creative capabilities",
  "Helpful and polite tone",
  "Good context handling",
  [
    562,
    563,
    564,
    565
  ],
  "Quick summaries",
  "Brainstorming",
  "Casual Q&A",
  "Short story writing",
  "2024-10-22",
  {
    "_8": 568,
    "_368": 569,
    "_370": 570,
    "_372": 571,
    "_373": 572,
    "_375": 573,
    "_380": 574,
    "_51": 445,
    "_385": 575,
    "_388": 576,
    "_390": 577,
    "_402": 579,
    "_408": 582,
    "_414": 587,
    "_420": 591,
    "_26": 445,
    "_430": 387
  },
  "claude-3-opus-20240229",
  "Claude 3 Opus",
  "Claude 3 Opus is Anthropic's most intelligent and capable model, designed for high-stakes, open-ended tasks requiring deep reasoning, creativity, and fluency. With unmatched comprehension and a 200K context window, it excels in research, strategic analysis, and complex automation.",
  "claude-3-opus",
  "/images/models/image-4.webp",
  [
    377
  ],
  [
    440,
    441,
    442,
    443
  ],
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 578
  },
  {},
  [
    462,
    580,
    553,
    581
  ],
  "Complex instructions",
  "Long brainstorming sessions",
  [
    583,
    584,
    585,
    586
  ],
  "Excellent context maintenance",
  "Well-structured responses",
  "Large input/output handling",
  "Enhanced creativity",
  [
    588,
    589,
    463,
    590
  ],
  "Book chapter writing",
  "Lengthy discussions",
  "Creative content generation",
  "2024-02-29",
  {
    "_8": 593,
    "_368": 594,
    "_370": 595,
    "_372": 593,
    "_373": 596,
    "_375": 597,
    "_380": 598,
    "_51": 601,
    "_385": 602,
    "_388": 603,
    "_390": 604,
    "_402": 606,
    "_408": 612,
    "_414": 618,
    "_420": 624,
    "_26": 601,
    "_430": 387
  },
  "deepseek-chat",
  "Deepseek Chat",
  "DeepSeek Chat is a powerful AI assistant known for its \"truth-seeking\" approach to conversations. It combines factual accuracy with a vast knowledge base to provide detailed, well-supported answers. The model excels at simplifying complex information while maintaining depth, making it ideal for both everyday tasks and in-depth research.",
  "/images/models/image-6.webp",
  [
    377
  ],
  [
    599,
    442,
    600,
    443
  ],
  "Conversation",
  "Knowledge",
  "Deepseek",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 605
  },
  {},
  [
    607,
    608,
    609,
    610,
    611
  ],
  "Factual Q&A",
  "Detailed explanations",
  "Document summarization",
  "Information-rich discussions",
  "Current topics and research",
  [
    613,
    614,
    615,
    616,
    617
  ],
  "High accuracy and thoroughness",
  "Broad knowledge scope",
  "Clear communication of complex topics",
  "Strong fact-checking capabilities",
  "Effective at handling clarifications",
  [
    619,
    620,
    621,
    622,
    623
  ],
  "Research assistance",
  "Document analysis",
  "Educational support",
  "Fact verification",
  "Detailed topic exploration",
  "2024-03-01",
  {
    "_8": 626,
    "_368": 627,
    "_370": 628,
    "_372": 626,
    "_373": 629,
    "_375": 630,
    "_380": 631,
    "_51": 601,
    "_385": 633,
    "_388": 634,
    "_390": 635,
    "_402": 639,
    "_408": 645,
    "_414": 651,
    "_420": 624,
    "_26": 601,
    "_430": 387
  },
  "deepseek-reasoner",
  "Deepseek Reasoner",
  "DeepSeek Reasoner is a specialized AI model designed to tackle complex problems with clear, step-by-step thinking. It focuses on providing a structured approach to answers, breaking down solutions into parts and walking through the reasoning process. Built to be thorough and methodical, it excels at explaining how answers are reached, not just providing the answers themselves.",
  "/images/models/image-5.webp",
  [
    377
  ],
  [
    484,
    632,
    442,
    443
  ],
  "Problem-solving",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 636
  },
  {
    "_450": 637
  },
  {
    "_452": 453,
    "_638": 32
  },
  "disabled",
  [
    640,
    641,
    642,
    643,
    644
  ],
  "Complex questions requiring chain-of-thought",
  "Multi-step math problems",
  "Logic puzzles",
  "Planning tasks",
  "Understanding solution processes",
  [
    646,
    647,
    648,
    649,
    650
  ],
  "Well-explained solutions with transparent thinking",
  "Strong attention to detail",
  "Systematic problem-solving approach",
  "Excellent for debugging and analysis",
  "Clear justification of results",
  [
    652,
    653,
    654,
    655,
    656
  ],
  "Teaching and learning complex concepts",
  "Technical problem-solving",
  "Decision analysis",
  "Systematic troubleshooting",
  "Step-by-step explanations",
  {
    "_8": 658,
    "_368": 659,
    "_370": 660,
    "_372": 661,
    "_373": 662,
    "_375": 663,
    "_380": 664,
    "_51": -5,
    "_385": 668,
    "_388": 669,
    "_390": 670,
    "_401": 32,
    "_402": 672,
    "_408": 678,
    "_414": 684,
    "_420": 690,
    "_26": 691,
    "_430": 387
  },
  "gemini-2.5-flash-preview-04-17",
  "Gemini 2.5 Flash",
  "Gemini 2.5 Flash (Preview 04-17) is a sneak peek at the ultra-fast variant of Google's Gemini 2.5 model. This preview indicates that Google was testing an accelerated version of their latest model around April 17th. It delivers answers at high speed, tailored for interactive settings, while still benefiting from Gemini 2.5's improved understanding and reasoning. In essence, you get a responsive experience that's more advanced than earlier Flash versions, because it's built on the smarter 2.5 generation.",
  "gemini-2-5-flash-preview-04-17",
  "/images/models/image-14.webp",
  [
    377,
    379
  ],
  [
    546,
    665,
    666,
    667
  ],
  "Cost-efficient",
  "Hybrid reasoning",
  "Controllable thinking",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 671
  },
  {},
  [
    673,
    674,
    675,
    676,
    677
  ],
  "Rapid Q&A and interactive tasks",
  "Smart assistant applications",
  "Quick brainstorming sessions",
  "Real-time conversation support",
  "Instant explanations and suggestions",
  [
    679,
    680,
    681,
    682,
    683
  ],
  "Combines swift replies with Gemini 2.5 brainpower",
  "Robust performance on typical tasks",
  "Quick multimodal processing",
  "Real-time response capabilities",
  "Balanced speed and intelligence",
  [
    685,
    686,
    687,
    688,
    689
  ],
  "Interactive chatbots",
  "Real-time tutoring",
  "Quick brainstorming",
  "Rapid information retrieval",
  "Live conversation support",
  "2024-04-17",
  "GoogleGenerative",
  {
    "_8": 693,
    "_368": 694,
    "_370": 695,
    "_372": 696,
    "_373": 697,
    "_375": 698,
    "_380": 699,
    "_51": -5,
    "_385": 703,
    "_388": 704,
    "_390": 705,
    "_401": 32,
    "_402": 707,
    "_408": 713,
    "_414": 719,
    "_420": 725,
    "_26": 691,
    "_430": 387
  },
  "gemini-2.5-pro-preview-05-06",
  "Gemini 2.5 Pro",
  "Gemini 2.5 Pro is Google's most powerful AI model, designed for deep reasoning, complex coding, and large-scale multimodal comprehension. With a 1M-token context window (2M coming soon), it excels at building web apps, transforming code, and solving intricate problems across vast datasets.",
  "gemini-2-5-pro-preview-05-06",
  "/images/models/image-35.webp",
  [
    377,
    379
  ],
  [
    700,
    701,
    702
  ],
  "Deep reasoning",
  "Complex coding",
  "Large-scale multimodal comprehension",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 706
  },
  {},
  [
    708,
    709,
    710,
    711,
    712
  ],
  "Detailed research queries",
  "Complex coding and debugging",
  "High-end usage requiring current and intelligent responses",
  "Multimodal comprehension tasks",
  "Large-scale data analysis",
  [
    714,
    715,
    716,
    717,
    718
  ],
  "Combines powerful reasoning with improved stability",
  "Excellent at following intricate instructions",
  "Multimodal processing capabilities",
  "Cutting-edge performance with ongoing fine-tuning",
  "Leverages Google's vast information resources",
  [
    720,
    721,
    722,
    723,
    724
  ],
  "Advanced research and analysis",
  "Complex software development",
  "Detailed technical documentation",
  "Multimodal content understanding",
  "High-stakes decision support",
  "2024-05-06",
  {
    "_8": 727,
    "_368": 728,
    "_370": 729,
    "_372": 730,
    "_373": 731,
    "_375": 732,
    "_380": 733,
    "_51": -5,
    "_385": 737,
    "_388": 738,
    "_390": 739,
    "_402": 741,
    "_408": 747,
    "_414": 753,
    "_420": 624,
    "_26": 758,
    "_430": 387
  },
  "mistral-large-latest",
  "Mistral Large",
  "Mistral Large is Mistral AI's flagship language model, built for high-level reasoning, multilingual understanding, and advanced code generation. With a 32K context window, strong benchmark performance, native function calling, and structured output support, it's ideal for enterprise-grade applications in RAG, automation, and multilingual workflows.",
  "mistral-large",
  "/images/models/image-17.webp",
  [
    377
  ],
  [
    734,
    735,
    736
  ],
  "High-level reasoning",
  "Multilingual understanding",
  "Advanced code generation",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 740
  },
  {},
  [
    742,
    743,
    744,
    745,
    746
  ],
  "General-purpose AI tasks",
  "Content generation and analysis",
  "Code development and support",
  "Custom AI solution development",
  "Enterprise-grade applications",
  [
    748,
    749,
    750,
    751,
    752
  ],
  "Open-source and customizable",
  "Strong performance across domains",
  "Extended context window",
  "Native function calling support",
  "Optimized for efficiency",
  [
    754,
    755,
    756,
    736,
    757
  ],
  "Custom AI application development",
  "Enterprise automation",
  "Multilingual content processing",
  "RAG system implementation",
  "Mistral",
  {
    "_8": 760,
    "_368": 761,
    "_370": 762,
    "_372": 763,
    "_373": 764,
    "_375": 765,
    "_380": 766,
    "_51": -5,
    "_385": 770,
    "_388": 771,
    "_390": 772,
    "_402": 774,
    "_408": 780,
    "_414": 786,
    "_420": 624,
    "_26": 758,
    "_430": 387
  },
  "mistral-small-latest",
  "Mistral Small",
  "Mistral Small is an optimized low-latency model designed for fast, cost-efficient language tasks. It supports RAG and function calling, offering excellent performance-to-cost ratio—making it perfect for lightweight production use, chat assistants, and real-time applications.",
  "mistral-small",
  "/images/models/image-18.webp",
  [
    377
  ],
  [
    767,
    665,
    768,
    769
  ],
  "Low-latency",
  "RAG",
  "Function calling",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 773
  },
  {},
  [
    775,
    776,
    777,
    778,
    779
  ],
  "Basic conversational AI",
  "Quick text processing",
  "Low-power device applications",
  "Simple query handling",
  "Real-time assistance",
  [
    781,
    782,
    783,
    784,
    785
  ],
  "Extremely fast and lightweight",
  "Efficient resource usage",
  "Good general knowledge",
  "Runs on consumer hardware",
  "Cost-effective operation",
  [
    787,
    788,
    789,
    790,
    791
  ],
  "Smart device assistants",
  "Quick text summarization",
  "Basic chat applications",
  "Simple parsing tasks",
  "Resource-constrained environments",
  {
    "_8": 793,
    "_368": 794,
    "_370": 795,
    "_372": 796,
    "_373": 797,
    "_375": 798,
    "_380": 799,
    "_51": -5,
    "_385": 803,
    "_388": 804,
    "_390": 805,
    "_402": 807,
    "_408": 813,
    "_414": 819,
    "_420": 624,
    "_26": 758,
    "_430": 387
  },
  "pixtral-large-latest",
  "Pixtral Large",
  "Pixtral Large is a 124B open-weight multimodal model built on Mistral Large 2, designed for deep understanding of text, images, charts, and documents. With a 128K context window and strong benchmark results, it excels at OCR, multilingual reasoning, and visual comprehension—making it the leading open model for complex multimodal tasks.",
  "pixtral-large",
  "/images/models/image-15.webp",
  [
    377
  ],
  [
    800,
    801,
    802
  ],
  "OCR",
  "Multilingual reasoning",
  "Visual comprehension",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 806
  },
  {},
  [
    808,
    809,
    810,
    811,
    812
  ],
  "Image interpretation and analysis",
  "Document understanding and OCR",
  "Visual question answering",
  "Chart and diagram analysis",
  "Multimodal reasoning tasks",
  [
    814,
    815,
    816,
    817,
    818
  ],
  "Exceptional image comprehension capabilities",
  "Deep understanding of visual and textual content",
  "Large 128K context window",
  "Strong multilingual support",
  "Leading open model for multimodal tasks",
  [
    820,
    821,
    822,
    823,
    824
  ],
  "Technical drawing analysis",
  "Document scanning and interpretation",
  "Visual data analysis",
  "Multilingual document processing",
  "Complex visual reasoning tasks",
  {
    "_8": 826,
    "_368": 827,
    "_370": 828,
    "_372": 826,
    "_373": 829,
    "_375": 830,
    "_380": 831,
    "_51": -5,
    "_385": 833,
    "_388": 834,
    "_390": 835,
    "_401": 32,
    "_402": 840,
    "_408": 845,
    "_414": 850,
    "_420": 855,
    "_422": 856,
    "_26": 384,
    "_430": 387
  },
  "gpt-4.1",
  "GPT-4.1",
  "OpenAI's latest flagship model, GPT-4.1, offers enhanced performance in coding, instruction following, and long-context comprehension. It supports up to a 1 million token context window and is optimized for powering AI agents.",
  "/images/models/image-21.webp",
  [
    377,
    378,
    379
  ],
  [
    832,
    384,
    121
  ],
  "Latest",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 836
  },
  {
    "_393": 837
  },
  {
    "_395": 838
  },
  {
    "_397": 839
  },
  [
    399,
    400
  ],
  [
    841,
    842,
    843,
    844
  ],
  "Detailed writing projects",
  "Complex question answering",
  "Coding and debugging",
  "Nuanced advice and brainstorming",
  [
    846,
    847,
    848,
    849
  ],
  "Highly accurate and coherent outputs",
  "Improved coding capabilities",
  "Longer attention span",
  "More efficient and cost-effective than GPT-4o",
  [
    851,
    852,
    853,
    854
  ],
  "Professional research",
  "Development",
  "Personal assistance",
  "Learning and planning",
  "2025",
  {
    "_368": 857,
    "_425": 858
  },
  "Meet GPT-4.1: Your expert assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 860,
    "_368": 861,
    "_370": 862,
    "_372": 860,
    "_373": 863,
    "_375": 864,
    "_380": 865,
    "_51": -5,
    "_385": 866,
    "_388": 867,
    "_390": 868,
    "_401": 32,
    "_402": 873,
    "_408": 878,
    "_414": 883,
    "_420": 855,
    "_422": 888,
    "_26": 384,
    "_430": 387
  },
  "gpt-4.1-mini",
  "GPT-4.1 Mini",
  "A mid-sized version of GPT-4.1 that balances performance and efficiency. GPT-4.1 Mini runs faster and is more cost-effective than the full model, achieving a new level of competence in small model performance.",
  "/images/models/image-22.webp",
  [
    377,
    378,
    379
  ],
  [
    382,
    383,
    384,
    121
  ],
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 869
  },
  {
    "_393": 870
  },
  {
    "_395": 871
  },
  {
    "_397": 872
  },
  [
    399,
    400
  ],
  [
    874,
    875,
    876,
    877
  ],
  "Personal assistants",
  "Customer service bots",
  "Batch text processing",
  "Quick translations and proofreading",
  [
    879,
    880,
    881,
    882
  ],
  "Quick and cost-effective",
  "Improved coding and reasoning skills",
  "Updated knowledge base",
  "Faster response times",
  [
    884,
    885,
    886,
    887
  ],
  "High-volume applications",
  "Budget-constrained projects",
  "Scaling AI features",
  "Less powerful hardware deployment",
  {
    "_368": 889,
    "_425": 890
  },
  "Meet GPT-4.1 Mini: Your expert assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 892,
    "_368": 893,
    "_370": 894,
    "_372": 892,
    "_373": 895,
    "_375": 896,
    "_380": 897,
    "_51": -5,
    "_385": 899,
    "_388": 900,
    "_390": 901,
    "_401": 32,
    "_402": 906,
    "_408": 911,
    "_414": 916,
    "_420": 421,
    "_422": 921,
    "_26": 384,
    "_430": 387
  },
  "gpt-4o",
  "GPT-4o",
  "GPT-4o is OpenAI's versatile, high-intelligence flagship model capable of real-time reasoning across audio, vision, and text. It integrates multiple data types simultaneously, enhancing accuracy and responsiveness in human-computer interactions.",
  "/images/models/image-23.webp",
  [
    377,
    378,
    379
  ],
  [
    898,
    384,
    121
  ],
  "Most capable",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 902
  },
  {
    "_393": 903
  },
  {
    "_395": 904
  },
  {
    "_397": 905
  },
  [
    399,
    400
  ],
  [
    907,
    908,
    909,
    910
  ],
  "Comprehensive research",
  "Intricate coding problems",
  "Detailed content generation",
  "Image analysis and discussion",
  [
    912,
    913,
    914,
    915
  ],
  "Exceptional reasoning",
  "Multimodal capabilities",
  "Thorough and well-structured answers",
  "Deep knowledge base",
  [
    917,
    918,
    919,
    920
  ],
  "Professional projects",
  "Academic research",
  "Image analysis",
  "Complex problem solving",
  {
    "_368": 922,
    "_425": 923
  },
  "Meet GPT-4o: Your expert assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 925,
    "_368": 926,
    "_370": 927,
    "_372": 925,
    "_373": 928,
    "_375": 929,
    "_380": 930,
    "_51": -5,
    "_385": 931,
    "_388": 932,
    "_390": 933,
    "_401": 32,
    "_402": 939,
    "_408": 944,
    "_414": 948,
    "_420": 952,
    "_422": 953,
    "_26": 384,
    "_430": 387
  },
  "o3",
  "O3",
  "OpenAI's O3 model is designed to advance reasoning capabilities across complex tasks like coding, math, science, and visual perception. It is the first reasoning model with access to autonomous tool use, including search, Python, and image generation.",
  "/images/models/image-25.webp",
  [
    377,
    378,
    438,
    379
  ],
  [
    832,
    384,
    121
  ],
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 934
  },
  {
    "_393": 935
  },
  {
    "_395": 936
  },
  {
    "_456": 937,
    "_397": 938
  },
  [
    458
  ],
  [
    399,
    400
  ],
  [
    940,
    941,
    942,
    943
  ],
  "Complex reasoning tasks",
  "Scientific analysis",
  "Advanced coding",
  "Visual perception tasks",
  [
    945,
    946,
    913,
    947
  ],
  "Autonomous tool use",
  "Advanced reasoning",
  "PDF analysis",
  [
    949,
    950,
    951,
    620
  ],
  "Research and development",
  "Scientific computing",
  "Advanced problem solving",
  "2023",
  {
    "_368": 954,
    "_425": 955
  },
  "Meet O3: Your expert assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 957,
    "_368": 958,
    "_370": 959,
    "_372": 957,
    "_373": 960,
    "_375": 961,
    "_380": 962,
    "_51": -5,
    "_385": 963,
    "_388": 964,
    "_390": 965,
    "_401": 32,
    "_402": 971,
    "_408": 976,
    "_414": 980,
    "_420": 421,
    "_422": 983,
    "_26": 384,
    "_430": 387
  },
  "o4-mini",
  "O4 Mini",
  "O4 Mini is OpenAI's newest small-footprint reasoning model, building on O3 Mini with markedly better performance in coding, math, science, and everyday problem-solving. It adds multimodal and tool capabilities previously limited to larger models.",
  "/images/models/image-26.webp",
  [
    377,
    378,
    379
  ],
  [
    382,
    383,
    384,
    121
  ],
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 966
  },
  {
    "_393": 967
  },
  {
    "_395": 968
  },
  {
    "_456": 969,
    "_397": 970
  },
  [
    458
  ],
  [
    399,
    400
  ],
  [
    972,
    973,
    974,
    975
  ],
  "Everyday problem-solving",
  "Basic coding tasks",
  "Quick math solutions",
  "Lightweight analysis",
  [
    977,
    913,
    978,
    979
  ],
  "Improved performance",
  "Tool access",
  "Cost-effective",
  [
    853,
    621,
    981,
    982
  ],
  "Basic development",
  "Quick analysis",
  {
    "_368": 984,
    "_425": 985
  },
  "Meet O4 Mini: Your expert assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 987,
    "_368": 988,
    "_370": 989,
    "_372": 987,
    "_373": 990,
    "_375": 991,
    "_380": 992,
    "_51": -5,
    "_385": 997,
    "_388": 998,
    "_390": 999,
    "_402": 1001,
    "_408": 1007,
    "_414": 1013,
    "_420": 624,
    "_422": 1018,
    "_26": 1021,
    "_430": 387
  },
  "grok-3",
  "Grok 3",
  "Grok-3 is xAI's latest flagship AI model and a major leap in their Grok series, built to rival the very best AIs. It brings an enormous knowledge base and advanced reasoning ability to any question. Users often note that Grok-3 feels like talking to an expert in many fields – it's exceptionally good in domains like law, finance, and healthcare, offering detailed and accurate explanations. It can also write complex code, break down hard problems step-by-step, and discuss almost any topic with depth. Grok-3 tends to have a frank and slightly witty style, making interactions both informative and engaging.",
  "/images/models/image-28.webp",
  [
    377
  ],
  [
    993,
    994,
    995,
    996
  ],
  "Enterprise-grade",
  "Expert knowledge",
  "Complex reasoning",
  "Code generation",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 1000
  },
  {},
  [
    1002,
    1003,
    1004,
    1005,
    1006
  ],
  "High-level consultations and expert advice",
  "Detailed analysis and complex Q&A",
  "Specialized topics in law, finance, and healthcare",
  "Creative and technical projects requiring top-tier AI",
  "In-depth research and analysis tasks",
  [
    1008,
    1009,
    1010,
    1011,
    1012
  ],
  "Comprehensive answers with strong factual accuracy",
  "Excellent at following nuanced instructions",
  "Clear explanation of complicated concepts",
  "Truth-seeking with evidence-based answers",
  "Expert-level knowledge across multiple domains",
  [
    1014,
    1015,
    1016,
    1017,
    653
  ],
  "Complex data analysis",
  "Academic research and exploration",
  "Professional consultation",
  "Innovative idea generation",
  {
    "_368": 1019,
    "_425": 1020
  },
  "Meet Grok 3: Your expert assistant.",
  [
    427,
    428,
    429
  ],
  "xAI",
  {
    "_8": 1023,
    "_368": 1024,
    "_370": 1025,
    "_372": 1023,
    "_373": 1026,
    "_375": 1027,
    "_380": 1028,
    "_51": -5,
    "_385": 1033,
    "_388": 1034,
    "_390": 1035,
    "_402": 1037,
    "_408": 1043,
    "_414": 1049,
    "_420": 624,
    "_422": 1055,
    "_26": 1021,
    "_430": 387
  },
  "grok-3-fast",
  "Grok 3 Fast",
  "Grok-3 Fast is the high-speed variant of the Grok-3 model, tailored for situations when quick answers are needed. It has the core knowledge and abilities of Grok-3 but is optimized for rapid responses, making it excellent for interactive applications where Grok-3's intelligence is desired but in a more nimble form. Grok-3 Fast maintains high-quality answers while delivering them more instantaneously.",
  "/images/models/image-29.webp",
  [
    377
  ],
  [
    1029,
    1030,
    1031,
    1032
  ],
  "High-speed",
  "Interactive",
  "Real-time",
  "Efficient",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 1036
  },
  {},
  [
    1038,
    1039,
    1040,
    1041,
    1042
  ],
  "Real-time chat systems",
  "Live interactive dialogues",
  "Low-latency applications",
  "Voice assistant integration",
  "High-traffic environments",
  [
    1044,
    1045,
    1046,
    1047,
    1048
  ],
  "Very responsive with maintained answer quality",
  "Effective handling of follow-up questions",
  "Near-standard Grok-3 performance at higher speeds",
  "Smooth dynamic exchanges",
  "Optimized for rapid query processing",
  [
    1050,
    1051,
    1052,
    1053,
    1054
  ],
  "Interactive applications",
  "Real-time customer support",
  "Voice interface integration",
  "High-frequency query systems",
  "Live chat implementations",
  {
    "_368": 1056,
    "_425": 1057
  },
  "Meet Grok 3 Fast: Your fast-thinking assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 1059,
    "_368": 1060,
    "_370": 1061,
    "_372": 1059,
    "_373": 1062,
    "_375": 1063,
    "_380": 1064,
    "_51": -5,
    "_385": 1069,
    "_388": 1070,
    "_390": 1071,
    "_402": 1073,
    "_408": 1078,
    "_414": 1083,
    "_420": 624,
    "_422": 1089,
    "_26": 1021,
    "_430": 387
  },
  "grok-3-mini",
  "Grok 3 Mini",
  "Grok-3 Mini is a lightweight, fast-thinking model designed for logic-driven tasks that don't require deep domain expertise. It offers transparent reasoning with accessible thought traces, making it ideal for lightweight applications, rapid iteration, and clear step-by-step logic.",
  "/images/models/image-30.webp",
  [
    377
  ],
  [
    1065,
    1066,
    1067,
    1068
  ],
  "Lightweight",
  "Fast-thinking",
  "Logic-driven",
  "Transparent",
  [
    387
  ],
  [
    387
  ],
  {
    "_387": 1072
  },
  {},
  [
    1074,
    1075,
    1076,
    1077,
    791
  ],
  "Lightweight applications",
  "Rapid iteration cycles",
  "Clear step-by-step logic",
  "Basic reasoning tasks",
  [
    1079,
    1080,
    782,
    1081,
    1082
  ],
  "Transparent reasoning process",
  "Accessible thought traces",
  "Quick response times",
  "Clear logical structure",
  [
    1084,
    1085,
    1086,
    1087,
    1088
  ],
  "Basic problem-solving",
  "Educational applications",
  "Simple automation tasks",
  "Quick logical analysis",
  "Resource-efficient deployments",
  {
    "_368": 1090,
    "_425": 1091
  },
  "Meet Grok 3-Mini: Your fast-thinking assistant.",
  [
    427,
    428,
    429
  ],
  {
    "_8": 1093,
    "_368": 1094,
    "_370": 1095,
    "_372": 1096,
    "_373": 1097,
    "_375": 1098,
    "_380": 1100,
    "_51": -5,
    "_385": 1104,
    "_388": 1105,
    "_390": 1106,
    "_402": 1151,
    "_408": 1157,
    "_414": 1163,
    "_420": 624,
    "_26": 1169,
    "_430": 373
  },
  "fal-ai/flux/schnell",
  "Flux Schnell",
  "Flux \"Schnell\" (German for \"fast\") is an AI image generator focused on speed. It delivers results very quickly while maintaining good quality, perfect for brainstorming or when you want many rapid variations of an image. While its output quality is slightly more basic compared to high-detail models, it excels in rapid concept generation and iterative workflows.",
  "flux-schnell",
  "/images/models/image-10.webp",
  [
    1099
  ],
  "Image Generation",
  [
    1101,
    1102,
    1103
  ],
  "Speed",
  "High-quality",
  "Rapid-prototyping",
  [
    387
  ],
  [
    373
  ],
  {
    "_373": 1107
  },
  {
    "_1108": 1109,
    "_1131": 1132,
    "_1146": 1147,
    "_1148": 1149
  },
  "aspectRatio",
  {
    "_390": 1110,
    "_452": 1113
  },
  [
    1111,
    1115,
    1117,
    1119,
    1121,
    1123,
    1125,
    1127,
    1129
  ],
  {
    "_1112": 1113,
    "_1114": 1113
  },
  "value",
  "1:1",
  "label",
  {
    "_1112": 1116,
    "_1114": 1116
  },
  "16:9",
  {
    "_1112": 1118,
    "_1114": 1118
  },
  "9:16",
  {
    "_1112": 1120,
    "_1114": 1120
  },
  "4:3",
  {
    "_1112": 1122,
    "_1114": 1122
  },
  "3:4",
  {
    "_1112": 1124,
    "_1114": 1124
  },
  "16:10",
  {
    "_1112": 1126,
    "_1114": 1126
  },
  "10:16",
  {
    "_1112": 1128,
    "_1114": 1128
  },
  "21:9",
  {
    "_1112": 1130,
    "_1114": 1130
  },
  "9:21",
  "n",
  {
    "_390": 1133,
    "_452": 1135
  },
  [
    1134,
    1137,
    1140,
    1143
  ],
  {
    "_1112": 1135,
    "_1114": 1136
  },
  "1",
  "1 image",
  {
    "_1112": 1138,
    "_1114": 1139
  },
  "2",
  "2 images",
  {
    "_1112": 1141,
    "_1114": 1142
  },
  "3",
  "3 images",
  {
    "_1112": 1144,
    "_1114": 1145
  },
  "4",
  "4 images",
  "seed",
  {},
  "guidanceImage",
  {
    "_390": 1150,
    "_452": 18
  },
  [],
  [
    1152,
    1153,
    1154,
    1155,
    1156
  ],
  "Rapid concept sketches",
  "Quick idea visualization",
  "Iterative design",
  "Brainstorming sessions",
  "Speed-focused workflows",
  [
    1158,
    1159,
    1160,
    1161,
    1162
  ],
  "Extremely fast image generation",
  "Efficient for iterative creativity",
  "Quick prompt variations",
  "Good quality for speed",
  "Ideal for rapid prototyping",
  [
    1164,
    1165,
    1166,
    1167,
    1168
  ],
  "Concept development",
  "Quick visualizations",
  "Design iterations",
  "Rapid prototyping",
  "Creative exploration",
  "Fal",
  {
    "_8": 1171,
    "_368": 1172,
    "_370": 1173,
    "_372": 1171,
    "_373": 1174,
    "_375": 1175,
    "_380": 1177,
    "_51": 1182,
    "_385": 1183,
    "_388": 1184,
    "_390": 1185,
    "_401": 34,
    "_402": 1189,
    "_408": 1195,
    "_414": 1201,
    "_420": 1207,
    "_422": 1208,
    "_1214": 1215,
    "_26": 1182,
    "_430": 373
  },
  "brett-ai",
  "Brett AI",
  "Alphakek Brett Model is a specialized AI image generation model focused exclusively on creating Brett-style memes from text prompts. It generates images in the distinctive Brett meme format, maintaining the characteristic style and visual elements that make Brett memes instantly recognizable.",
  "/images/models/image-brett.webp",
  [
    1099,
    1176
  ],
  "Meme Creation",
  [
    1178,
    1179,
    1180,
    1181
  ],
  "Brett Memes",
  "Meme Generation",
  "AI Art",
  "Brett Style",
  "Alphakek",
  [
    387
  ],
  [
    373
  ],
  {
    "_373": 1186
  },
  {
    "_1148": 1187
  },
  {
    "_390": 1188,
    "_452": 18
  },
  [],
  [
    1190,
    1191,
    1192,
    1193,
    1194
  ],
  "Creating Brett-style memes",
  "Generating Brett meme variations",
  "Producing Brett meme templates",
  "Developing Brett meme content",
  "Creating Brett-style image macros",
  [
    1196,
    1197,
    1198,
    1199,
    1200
  ],
  "Specialized Brett meme generation",
  "Consistent Brett meme style",
  "High-quality Brett meme output",
  "Fast Brett meme generation",
  "Authentic Brett meme format",
  [
    1202,
    1203,
    1204,
    1205,
    1206
  ],
  "Brett meme creation",
  "Brett meme variations",
  "Brett meme templates",
  "Brett meme content",
  "Brett-style image generation",
  "2024-01-01",
  {
    "_368": 1209,
    "_425": 1210
  },
  "Meet Brett AI: Your Brett-style meme generator.",
  [
    1211,
    1212,
    1213
  ],
  "Brett drinking coffee",
  "Brett saying \"good morning\"",
  "Brett on the moon",
  "key",
  "2d6ad0f1599ad11f89d48ca52b88048fe295ef093a102455ac4499c2cea8727b",
  {
    "_8": 1217,
    "_368": 1218,
    "_370": 1219,
    "_372": 1220,
    "_373": 1221,
    "_375": 1222,
    "_380": 1223,
    "_51": -5,
    "_385": 1227,
    "_388": 1228,
    "_390": 1106,
    "_402": 1229,
    "_408": 1235,
    "_414": 1241,
    "_420": 624,
    "_26": 1169,
    "_430": 373
  },
  "fal-ai/flux-pro/v1.1-ultra",
  "Flux Pro v1.1 Ultra",
  "Flux Pro v1.1 Ultra is a high-end image generation model geared toward producing ultra-detailed, high-resolution images. It captures images in sharp 4K-like quality with rich details, creating lifelike photos of people and scenes or very intricate and polished artwork. While it may take longer to generate, the results are often stunning, making it ideal for when quality is your top priority.",
  "flux-pro-ultra",
  "/images/models/image-7.webp",
  [
    1099
  ],
  [
    1102,
    1224,
    1225,
    1226
  ],
  "Photography",
  "Realism",
  "Professional-grade",
  [
    387
  ],
  [
    373
  ],
  [
    1230,
    1231,
    1232,
    1233,
    1234
  ],
  "Photorealistic images",
  "Detailed illustrations",
  "Marketing visuals",
  "Concept art",
  "High-resolution outputs",
  [
    1236,
    1237,
    1238,
    1239,
    1240
  ],
  "Extremely high image quality with fine details",
  "Realistic lighting and textures",
  "High resolution support",
  "Accurate and vivid results",
  "Enhanced realism modes",
  [
    1242,
    1243,
    1244,
    1245,
    1246
  ],
  "Professional photography",
  "Book covers",
  "Presentation visuals",
  "Realistic renderings",
  "High-end marketing materials",
  {
    "_8": 1248,
    "_368": 1249,
    "_370": 1250,
    "_372": 1251,
    "_373": 1252,
    "_375": 1253,
    "_380": 1254,
    "_51": -5,
    "_385": 1259,
    "_388": 1260,
    "_390": 1106,
    "_402": 1261,
    "_408": 1267,
    "_414": 1273,
    "_420": 624,
    "_26": 1169,
    "_430": 373
  },
  "fal-ai/flux/dev",
  "Flux Dev",
  "Flux Dev is the experimental version of the Flux image model, showcasing the latest features and improvements that are still in testing. It gives you access to cutting-edge capabilities and new art styles before they're officially released. While results can occasionally be inconsistent, it offers exciting opportunities for creative exploration and innovation.",
  "flux-dev",
  "/images/models/image-12.webp",
  [
    1099
  ],
  [
    1255,
    1256,
    1257,
    1258
  ],
  "Experimental",
  "Cutting-edge",
  "Innovation",
  "Creative",
  [
    387
  ],
  [
    373
  ],
  [
    1262,
    1263,
    1264,
    1265,
    1266
  ],
  "Exploring new features",
  "Trying latest styles",
  "Creative experimentation",
  "Innovation testing",
  "Feature preview",
  [
    1268,
    1269,
    1270,
    1271,
    1272
  ],
  "Access to newest generation techniques",
  "Enhanced fidelity in some cases",
  "Greater creative diversity",
  "Early access to improvements",
  "Innovative capabilities",
  [
    1168,
    1274,
    1275,
    1276,
    1277
  ],
  "Feature testing",
  "Style experimentation",
  "Innovation research",
  "Development preview",
  {
    "_8": 1279,
    "_368": 1280,
    "_370": 1281,
    "_372": 1279,
    "_373": 1282,
    "_375": 1283,
    "_380": 1284,
    "_51": -5,
    "_385": 1289,
    "_388": 1290,
    "_390": 1291,
    "_402": 1308,
    "_408": 1314,
    "_414": 1320,
    "_420": 1325,
    "_26": 384,
    "_430": 373
  },
  "dall-e-3",
  "DALL·E 3",
  "DALL·E 3 is OpenAI's flagship image generation model known for its creativity and fidelity. With DALL·E 3, you can describe any scene or idea, and the model will paint it for you in incredible detail. It's particularly user-friendly: it tends to follow prompts more closely than earlier versions, so if you ask for \"a purple dragon flying over New York City at sunset,\" DALL·E 3 will actually give you just that, often with astonishing quality.",
  "/images/models/image-19.webp",
  [
    1099
  ],
  [
    1285,
    1286,
    1287,
    1288
  ],
  "High-quality visuals",
  "Fast generation",
  "Strong prompt adherence",
  "Consistent aesthetic quality",
  [
    387
  ],
  [
    373
  ],
  {
    "_373": 1292
  },
  {
    "_1293": 1294,
    "_1131": 1302,
    "_1148": 1306
  },
  "size",
  {
    "_390": 1295,
    "_452": 1297
  },
  [
    1296,
    1298,
    1300
  ],
  {
    "_1112": 1297,
    "_1114": 1297
  },
  "1024x1024",
  {
    "_1112": 1299,
    "_1114": 1299
  },
  "1792x1024",
  {
    "_1112": 1301,
    "_1114": 1301
  },
  "1024x1792",
  {
    "_390": 1303,
    "_452": 1135
  },
  [
    1304,
    1305
  ],
  {
    "_1112": 1135,
    "_1114": 1136
  },
  {
    "_1112": 1138,
    "_1114": 1139
  },
  {
    "_390": 1307,
    "_452": 18
  },
  [],
  [
    1309,
    1310,
    1311,
    1312,
    1313
  ],
  "Turning imaginative concepts into images",
  "Creating art and illustrations",
  "Design mockups from text descriptions",
  "Visualizing stories and narratives",
  "Generating unique artwork",
  [
    1315,
    1316,
    1317,
    1318,
    1319
  ],
  "Highly creative and versatile",
  "Handles complex or whimsical prompts very well",
  "Detailed and coherent image outputs",
  "Significant improvement in prompt understanding",
  "Better handling of text in images",
  [
    1321,
    1322,
    1323,
    1324,
    1233
  ],
  "Artwork generation",
  "Story visualization",
  "Project graphics",
  "Creative design",
  "2023-11-01",
  {
    "_8": 1327,
    "_368": 1328,
    "_370": 1329,
    "_372": 1327,
    "_373": 1330,
    "_375": 1331,
    "_380": 1332,
    "_51": -5,
    "_385": 1333,
    "_388": 1334,
    "_390": 1335,
    "_402": 1347,
    "_408": 1353,
    "_414": 1359,
    "_420": 624,
    "_26": 384,
    "_430": 373
  },
  "gpt-image-1",
  "GPT Image 1",
  "GPT-Image 1 is OpenAI's advanced image generation model, part of the GPT-4o family, capable of turning text prompts into vivid images. It represents a new wave of AI that natively combines language understanding with image creation. GPT-Image 1 can produce remarkably photorealistic pictures and imaginative art simply by describing what you want. It's more powerful than the older DALL-E models, often yielding images that closely match your description – even handling complex scenes well.",
  "/images/models/image-20.webp",
  [
    1099
  ],
  [
    1285,
    1286,
    1287,
    1288
  ],
  [
    387
  ],
  [
    373
  ],
  {
    "_373": 1336
  },
  {
    "_1293": 1337,
    "_1131": 1344,
    "_1148": 1345
  },
  {
    "_390": 1338,
    "_452": 1297
  },
  [
    1339,
    1340,
    1342
  ],
  {
    "_1112": 1297,
    "_1114": 1297
  },
  {
    "_1112": 1341,
    "_1114": 1341
  },
  "1536x1024",
  {
    "_1112": 1343,
    "_1114": 1343
  },
  "1024x1536",
  {
    "_390": 1303,
    "_452": 1135
  },
  {
    "_390": 1346,
    "_452": 18
  },
  [],
  [
    1348,
    1349,
    1350,
    1351,
    1352
  ],
  "Generating high-quality images from text descriptions",
  "Creating realistic photographs",
  "Developing creative art concepts",
  "Integrated chat-based image refinement",
  "Professional-grade visual outputs",
  [
    1354,
    1355,
    1356,
    1357,
    1358
  ],
  "Excellent understanding of detailed prompts",
  "Produces very realistic people, landscapes, and objects",
  "Integrated into chat workflows",
  "Captures nuances in descriptions",
  "Handles complex scenes effectively",
  [
    1360,
    1361,
    1362,
    1363,
    1364
  ],
  "Concept art creation",
  "Photorealistic scene generation",
  "Conversational image refinement",
  "Professional design work",
  "Visual storytelling",
  {
    "_8": 1366,
    "_368": 1367,
    "_370": 1368,
    "_372": 1366,
    "_373": 1369,
    "_375": 1370,
    "_380": 1372,
    "_51": -5,
    "_385": 1375,
    "_388": 1376,
    "_390": 1377,
    "_402": 1388,
    "_408": 1394,
    "_414": 1400,
    "_420": 624,
    "_26": 1021,
    "_430": 373
  },
  "grok-2-image",
  "Grok 2 Image",
  "Grok-2 Image is an image generation model from xAI's Grok series, extending the capabilities of Grok-2 into the visual domain. This model can create pictures from text prompts, combining the knowledge prowess of Grok with creative image synthesis. It's useful for generating visual content with the added context that Grok's training provides, making it particularly effective for crafting memes or specific styles of art that benefit from cultural and niche references.",
  "/images/models/image-27.webp",
  [
    1099,
    1371,
    1176
  ],
  "Creative Art",
  [
    1180,
    1179,
    1373,
    1374
  ],
  "Visual Content",
  "Grok Integration",
  [
    387
  ],
  [
    373
  ],
  {
    "_373": 1378
  },
  {
    "_1131": 1379,
    "_1146": 1385,
    "_1148": 1386
  },
  {
    "_390": 1380,
    "_452": 1135
  },
  [
    1381,
    1382,
    1383,
    1384
  ],
  {
    "_1112": 1135,
    "_1114": 1136
  },
  {
    "_1112": 1138,
    "_1114": 1139
  },
  {
    "_1112": 1141,
    "_1114": 1142
  },
  {
    "_1112": 1144,
    "_1114": 1145
  },
  {},
  {
    "_390": 1387,
    "_452": 18
  },
  [],
  [
    1389,
    1390,
    1391,
    1392,
    1393
  ],
  "General image generation tasks",
  "Creating memes with cultural context",
  "Generating context-rich illustrations",
  "Integrated Grok ecosystem workflows",
  "Combining chat and image generation",
  [
    1395,
    1396,
    1397,
    1398,
    1399
  ],
  "Benefits from Grok's broad training and knowledge base",
  "Versatile output from cartoonish to realistic scenes",
  "Less restricted content generation",
  "Seamless integration with Grok chat",
  "Strong cultural and contextual understanding",
  [
    1401,
    1402,
    1403,
    1404,
    1405
  ],
  "Meme creation and viral content",
  "Context-aware illustration",
  "Integrated AI workflows",
  "Creative visual content",
  "Educational and explanatory visuals",
  {
    "_8": 1407,
    "_368": 1408,
    "_370": 1409,
    "_372": 1410,
    "_373": 1221,
    "_375": 1411,
    "_380": 1413,
    "_51": -5,
    "_385": 1418,
    "_388": 1419,
    "_390": 1420,
    "_402": 1437,
    "_408": 1440,
    "_414": 1444,
    "_420": 624,
    "_422": 1448,
    "_26": 1454,
    "_430": 373
  },
  "imagen-3.0-generate-002",
  "Vertex Imagen 3",
  "High-quality image generation using Google Vertex AI Imagen 3, ideal for detailed and polished outputs.",
  "imagen-3-generate-002",
  [
    1412
  ],
  "AI Image Generation",
  [
    1414,
    1415,
    1416,
    1417
  ],
  "high-quality",
  "realistic",
  "google",
  "imagen",
  [
    387
  ],
  [
    373
  ],
  {
    "_373": 1421
  },
  {
    "_1108": 1422,
    "_1131": 1431,
    "_1146": 1435,
    "_1148": 1436
  },
  {
    "_390": 1423,
    "_452": 1113
  },
  [
    1424,
    1425,
    1427,
    1429,
    1430
  ],
  {
    "_1112": 1113,
    "_1114": 133
  },
  {
    "_1112": 1116,
    "_1114": 1426
  },
  "Landscape",
  {
    "_1112": 1118,
    "_1114": 1428
  },
  "Portrait",
  {
    "_1112": 1120,
    "_1114": 1426
  },
  {
    "_1112": 1122,
    "_1114": 1428
  },
  {
    "_390": 1432,
    "_452": 1135
  },
  [
    1433,
    1434
  ],
  {
    "_1112": 1135,
    "_1114": 1136
  },
  {
    "_1112": 1138,
    "_1114": 1139
  },
  {},
  {},
  [
    1232,
    1438,
    1439
  ],
  "High-res renders",
  "Product design",
  [
    1441,
    1442,
    1443
  ],
  "Photorealism",
  "Lighting",
  "Detail",
  [
    1445,
    1446,
    1447
  ],
  "Brand visuals",
  "Illustration",
  "Mockups",
  {
    "_368": 1449,
    "_425": 1450
  },
  "Generate high-quality images with Google Vertex AI Imagen 3",
  [
    1451,
    1452,
    1453
  ],
  "Generate a tiger in the jungle",
  "Generate a sunset over the ocean",
  "Generate a nightclub",
  "GoogleVertex",
  {
    "_8": 1456,
    "_368": 1457,
    "_370": 1458,
    "_372": 1459,
    "_373": 1460,
    "_375": 1461,
    "_380": 1462,
    "_51": -5,
    "_385": 1464,
    "_388": 1465,
    "_390": 1420,
    "_402": 1466,
    "_408": 1469,
    "_414": 1472,
    "_420": 624,
    "_422": 1476,
    "_26": 1454,
    "_430": 373
  },
  "imagen-3.0-fast-generate-001",
  "Vertex Imagen 3 Fast",
  "Fast image generation using Google Vertex AI Imagen 3, optimized for speed and responsiveness.",
  "imagen-3-fast-generate-001",
  "/images/models/image-8.webp",
  [
    1412
  ],
  [
    1463,
    1415,
    1416,
    1417
  ],
  "fast",
  [
    387
  ],
  [
    373
  ],
  [
    1467,
    1468
  ],
  "Quick image previews",
  "Fast concept ideation",
  [
    1101,
    1470,
    1471
  ],
  "Low latency generation",
  "Decent quality",
  [
    1473,
    1474,
    1475
  ],
  "Storyboarding",
  "UI mockups",
  "Social media content",
  {
    "_368": 1477,
    "_425": 1478
  },
  "Generate images fast with Google Vertex AI Imagen 3",
  [
    1451,
    1452,
    1453
  ]
]
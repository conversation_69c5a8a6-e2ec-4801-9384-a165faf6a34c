#!/usr/bin/env -S deno run --allow-net

/**
 * Vertical OpenAI Compatible API - Deno Single File Implementation
 *
 * 这是一个完全自包含的 Deno 服务，将 Vertical API 转换为 OpenAI 兼容的 API 格式。
 *
 * 使用方法:
 * 1. 修改下面的 MODELS_DATA 配置
 * 2. 运行: deno run --allow-net main.ts
 * 3. 发送请求时支持两种认证模式:
 *    模式1 - 直接使用令牌:
 *    - Authorization: Bearer <client-api-key>  (用于客户端认证)
 *    - X-Vertical-Token: <complete-cookies>    (用于 Vertical API 调用，完整的 cookie 字符串)
 *
 *    模式2 - 用户名密码登录:
 *    - Authorization: Bearer <username>---<password>  (用户名和密码用三个短横线分隔)
 *
 * 示例请求 (模式1):
 * curl -X POST http://localhost:8000/v1/chat/completions \
 *   -H "Authorization: Bearer sk-your-custom-key-here" \
 *   -H "X-Vertical-Token: deviceId=...; sb-ppdjlmajmpcqpkdmnzfd-auth-token=...; _clck=..." \
 *   -H "Content-Type: application/json" \
 *   -d '{"model": "gpt-4o", "messages": [{"role": "user", "content": "Hello!"}]}'
 *
 * 示例请求 (模式2):
 * curl -X POST http://localhost:8000/v1/chat/completions \
 *   -H "Authorization: Bearer <EMAIL>---password123" \
 *   -H "Content-Type: application/json" \
 *   -d '{"model": "gpt-4o", "messages": [{"role": "user", "content": "Hello!"}]}'
 */

import { Application, Router, Context } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { crypto } from "https://deno.land/std@0.208.0/crypto/mod.ts";

// ===== 配置常量 =====
const CONVERSATION_CACHE_MAX_SIZE = 100;
const DEFAULT_REQUEST_TIMEOUT = 30000;
const PORT = 8000;

// ===== 内置配置 =====
// 从 model.json 动态生成模型配置
function loadModelsFromJson(): any {
  try {
    // 读取 model.json 文件内容
    const modelJsonContent = `{
  "models": [
    {
      "id": "gpt-4o-mini",
      "title": "GPT-4o Mini",
      "description": "A smaller, more affordable version of GPT-4o, GPT-4o Mini offers a balance of performance and cost-efficiency for various AI applications. It excels in text and vision tasks with extensive context support.",
      "slug": "gpt-4o-mini",
      "options": {
        "file": {
          "mimeType": "image/*",
          "extensions": [".png", ".jpg"]
        }
      },
      "supportsNativeWebSearch": true,
      "type": "text"
    },
    {
      "id": "claude-4-opus-20250514",
      "title": "Claude 4 Opus",
      "description": "Claude 4 Opus is Anthropic's most powerful model and the world's best coding model, leading on SWE-bench (72.5%) and Terminal-bench (43.2%). It delivers sustained performance on long-running tasks that require focused effort and thousands of steps, with the ability to work continuously for several hours—dramatically outperforming all Sonnet models and significantly expanding what AI agents can accomplish.",
      "slug": "claude-4-opus",
      "options": {
        "reasoning": {
          "defaultValue": "on"
        },
        "file": {
          "mimeType": "application/pdf",
          "extensions": [".pdf", ".jpg", ".png"]
        }
      },
      "type": "text"
    },
    {
      "id": "claude-4-sonnet-20250514",
      "title": "Claude 4 Sonnet",
      "description": "Claude 4 Sonnet significantly improves on Sonnet 3.7's industry-leading capabilities, excelling in coding with a state-of-the-art 72.7% on SWE-bench. The model balances performance and efficiency for internal and external use cases, with enhanced steerability for greater control over implementations. While not matching Opus 4 in most domains, it delivers an optimal mix of capability and practicality.",
      "slug": "claude-4-sonnet",
      "options": {
        "reasoning": {
          "defaultValue": "on"
        },
        "file": {
          "mimeType": "application/pdf",
          "extensions": [".pdf", ".jpg", ".png"]
        }
      },
      "type": "text"
    },
    {
      "id": "claude-3-7-sonnet-20250219",
      "title": "Claude 3.7 Sonnet",
      "description": "Claude 3.7 Sonnet is Anthropic's most advanced hybrid reasoning model, excelling at real-world coding, deep reasoning, and complex multi-step tasks. It supports fast responses or extended thinking, making it ideal for software development, structured writing, and technical problem-solving.",
      "slug": "claude-3-7-sonnet",
      "options": {
        "reasoning": {
          "defaultValue": "on"
        },
        "file": {
          "mimeType": "application/pdf",
          "extensions": [".pdf", ".jpg", ".png"]
        }
      },
      "type": "text"
    },
    {
      "id": "claude-3-5-haiku-20241022",
      "title": "Claude 3.5 Haiku",
      "description": "Claude 3.5 Haiku is Anthropic's fastest model, combining speed with strong performance in coding, instruction following, and real-time tasks. Ideal for chatbots, content moderation, and sub-agent workflows, it delivers high accuracy at a low cost, even outperforming larger models on many benchmarks.",
      "slug": "claude-3-5-haiku",
      "type": "text"
    },
    {
      "id": "deepseek-chat",
      "title": "Deepseek Chat",
      "description": "DeepSeek Chat is a powerful AI assistant known for its \\"truth-seeking\\" approach to conversations. It combines factual accuracy with a vast knowledge base to provide detailed, well-supported answers. The model excels at simplifying complex information while maintaining depth, making it ideal for both everyday tasks and in-depth research.",
      "slug": "deepseek-chat",
      "type": "text"
    },
    {
      "id": "gpt-4.1",
      "title": "GPT-4.1",
      "description": "OpenAI's latest flagship model, GPT-4.1, offers enhanced performance in coding, instruction following, and long-context comprehension. It supports up to a 1 million token context window and is optimized for powering AI agents.",
      "slug": "gpt-4.1",
      "options": {
        "file": {
          "mimeType": "image/*",
          "extensions": [".png", ".jpg"]
        }
      },
      "supportsNativeWebSearch": true,
      "type": "text"
    },
    {
      "id": "gpt-4.1-mini",
      "title": "GPT-4.1 Mini",
      "description": "A mid-sized version of GPT-4.1 that balances performance and efficiency. GPT-4.1 Mini runs faster and is more cost-effective than the full model, achieving a new level of competence in small model performance.",
      "slug": "gpt-4.1-mini",
      "options": {
        "file": {
          "mimeType": "image/*",
          "extensions": [".png", ".jpg"]
        }
      },
      "type": "text"
    },
    {
      "id": "gpt-4o",
      "title": "GPT-4o",
      "description": "GPT-4o is OpenAI's versatile, high-intelligence flagship model capable of real-time reasoning across audio, vision, and text. It integrates multiple data types simultaneously, enhancing accuracy and responsiveness in human-computer interactions.",
      "slug": "gpt-4o",
      "options": {
        "file": {
          "mimeType": "image/*",
          "extensions": [".png", ".jpg"]
        }
      },
      "type": "text"
    },
    {
      "id": "o3",
      "title": "O3",
      "description": "OpenAI's O3 model is designed to advance reasoning capabilities across complex tasks like coding, math, science, and visual perception. It is the first reasoning model with access to autonomous tool use, including search, Python, and image generation.",
      "slug": "o3",
      "options": {
        "file": {
          "mimeType": "application/pdf",
          "extensions": [".pdf", ".png", ".jpg"]
        }
      },
      "type": "text"
    },
    {
      "id": "o4-mini",
      "title": "O4 Mini",
      "description": "O4 Mini is OpenAI's newest small-footprint reasoning model, building on O3 Mini with markedly better performance in coding, math, science, and everyday problem-solving. It adds multimodal and tool capabilities previously limited to larger models.",
      "slug": "o4-mini",
      "options": {
        "file": {
          "mimeType": "application/pdf",
          "extensions": [".pdf", ".png", ".jpg"]
        }
      },
      "type": "text"
    },
    {
      "id": "grok-3",
      "title": "Grok 3",
      "description": "Grok-3 is xAI's latest flagship AI model and a major leap in their Grok series, built to rival the very best AIs. It brings an enormous knowledge base and advanced reasoning ability to any question. Users often note that Grok-3 feels like talking to an expert in many fields – it's exceptionally good in domains like law, finance, and healthcare, offering detailed and accurate explanations. It can also write complex code, break down hard problems step-by-step, and discuss almost any topic with depth. Grok-3 tends to have a frank and slightly witty style, making interactions both informative and engaging.",
      "slug": "grok-3",
      "type": "text"
    },
    {
      "id": "mistral-large-latest",
      "title": "Mistral Large",
      "description": "Mistral Large is Mistral AI's flagship language model, built for high-level reasoning, multilingual understanding, and advanced code generation. With a 32K context window, strong benchmark performance, native function calling, and structured output support, it's ideal for enterprise-grade applications in RAG, automation, and multilingual workflows.",
      "slug": "mistral-large",
      "type": "text"
    },
    {
      "id": "gemini-2.5-pro-preview-05-06",
      "title": "Gemini 2.5 Pro",
      "description": "Gemini 2.5 Pro is Google's most powerful AI model, designed for deep reasoning, complex coding, and large-scale multimodal comprehension. With a 1M-token context window (2M coming soon), it excels at building web apps, transforming code, and solving intricate problems across vast datasets.",
      "slug": "gemini-2-5-pro-preview-05-06",
      "type": "text"
    },
    {
      "id": "fal-ai/flux/schnell",
      "title": "Flux Schnell",
      "description": "Flux \\"Schnell\\" (German for \\"fast\\") is an AI image generator focused on speed. It delivers results very quickly while maintaining good quality, perfect for brainstorming or when you want many rapid variations of an image. While its output quality is slightly more basic compared to high-detail models, it excels in rapid concept generation and iterative workflows.",
      "slug": "flux-schnell",
      "type": "image"
    }
  ]
}`;

    const modelData = JSON.parse(modelJsonContent);

    // 转换模型数据为 main.ts 需要的格式
    const convertedModels = modelData.models
      .filter((model: any) => model.type === "text") // 只保留文本模型，过滤掉图像生成模型
      .map((model: any) => {
        // 判断是否支持推理功能
        const hasReasoningOption = model.options?.reasoning?.defaultValue === "on";

        return {
          id: model.id,
          object: "model",
          created: 1703980800,
          owned_by: "vertical-studio",
          vertical_model_id: model.id,
          vertical_model_url: `https://app.verticalstudio.ai/stream/models/${model.id}`,
          output_reasoning_flag: hasReasoningOption,
          description: model.description
        };
      });

    return {
      data: convertedModels
    };
  } catch (error) {
    console.error("加载模型配置失败，使用默认配置:", error);
    // 如果加载失败，返回默认配置
    return {
      data: [
        {
          id: "gpt-4o-mini",
          object: "model",
          created: 1703980800,
          owned_by: "vertical-studio",
          vertical_model_id: "gpt-4o-mini",
          vertical_model_url: "https://app.verticalstudio.ai/stream/models/gpt-4o-mini",
          output_reasoning_flag: false,
          description: "A smaller, more affordable version of GPT-4o, GPT-4o Mini offers a balance of performance and cost-efficiency for various AI applications."
        }
      ]
    };
  }
}

// 根据新的 Vertical Studio AI 接口格式更新
const MODELS_DATA = loadModelsFromJson();

// ===== 全局变量 =====
let conversationCache: Map<string, any> = new Map();

// ===== 类型定义 =====
interface ChatMessage {
  role: string;
  content: string;
}

interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

interface ModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  vertical_model_id?: string;
  vertical_model_url?: string;
  output_reasoning_flag?: boolean;
  description?: string;
}

interface StreamChoice {
  delta: Record<string, any>;
  index: number;
  finish_reason?: string;
}

interface StreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: StreamChoice[];
}

interface ChatCompletionChoice {
  message: ChatMessage;
  index: number;
  finish_reason: string;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// ===== 工具函数 =====
function generateUUID(): string {
  return crypto.randomUUID();
}

function getCurrentTimestamp(): number {
  return Math.floor(Date.now() / 1000);
}

async function sha256Hash(text: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
}

async function generateMessageFingerprint(role: string, content: string): Promise<string> {
  const hash = await sha256Hash(content);
  return `${role}:${hash}`;
}

// ===== 日志工具函数 =====
function formatTimestamp(): string {
  return new Date().toISOString();
}

function logInfo(requestId: string, step: string, message: string, data?: any): void {
  const timestamp = formatTimestamp();
  const logData = data ? ` | 数据: ${JSON.stringify(data, null, 2)}` : '';
  console.log(`[${timestamp}] [INFO] [${requestId}] [${step}] ${message}${logData}`);
}

function logError(requestId: string, step: string, message: string, error?: any): void {
  const timestamp = formatTimestamp();
  const errorData = error ? ` | 错误: ${error instanceof Error ? error.message : JSON.stringify(error)}` : '';
  console.error(`[${timestamp}] [ERROR] [${requestId}] [${step}] ${message}${errorData}`);
}

function logWarning(requestId: string, step: string, message: string, data?: any): void {
  const timestamp = formatTimestamp();
  const logData = data ? ` | 数据: ${JSON.stringify(data, null, 2)}` : '';
  console.warn(`[${timestamp}] [WARN] [${requestId}] [${step}] ${message}${logData}`);
}

// ===== 工具函数 =====

function getModelItem(modelId: string): ModelInfo | null {
  for (const model of MODELS_DATA.data) {
    if (model.id === modelId) {
      return model;
    }
  }
  return null;
}

// ===== 登录功能 =====
async function loginWithCredentials(email: string, password: string, requestId?: string): Promise<string | null> {
  const logId = requestId || "UNKNOWN";

  try {
    logInfo(logId, "LOGIN", "开始用户名密码登录", {
      email: email,
      passwordLength: password.length
    });

    // 将邮箱编码为 base64
    const emailBase64 = btoa(email);
    const loginUrl = `https://app.verticalstudio.ai/login-password.data?email=${encodeURIComponent(emailBase64)}`;

    logInfo(logId, "LOGIN", "发送登录请求", {
      url: loginUrl,
      emailBase64: emailBase64
    });

    // 构造登录请求
    const formData = new URLSearchParams();
    formData.append('email', email);
    formData.append('password', password);

    const response = await fetch(loginUrl, {
      method: "POST",
      headers: {
        'host': 'app.verticalstudio.ai',
        'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'sentry-trace': 'b1e352ce1aa747d89a65ce1f50264ced-b1a4e8081f1823b4-1',
        'sec-ch-ua-platform': '"Windows"',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
        'sec-ch-ua-mobile': '?0',
        'baggage': 'sentry-environment=production,sentry-public_key=0fb63d9554f3efa0c9fa41330d426e6a,sentry-trace_id=b1e352ce1aa747d89a65ce1f50264ced,sentry-sample_rate=1,sentry-sampled=true',
        'accept': '*/*',
        'origin': 'https://app.verticalstudio.ai',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'referer': `https://app.verticalstudio.ai/login-password?email=${encodeURIComponent(emailBase64)}`,
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cookie': 'deviceId=IjI1YWFiNmQ0LTRmN2ItNDA3Mi05MGQ1LTNkMGE4NTQ1YmJlMSI%3D.%2FyW1PGLxp8KZuXbBbMTNd%2BX%2BkQupSIv8WBTjthn3Too; initial_referer=eyJ1cmwiOiIkZGlyZWN0IiwiZG9tYWluIjoiJGRpcmVjdCJ9.I84i3pEgdLAC4TQsyKUOW%2F3aQVfQAn2DMnFEzqqzHAo; has_seen_survey_popup=true; _clck=1ouwwak%7C2%7Cfwl%7C0%7C1985; toast-session=e30%3D.; wagmi.walletConnect.requestedChains=[]; CookieConsent={stamp:%273FHHZaCavlMJkqnGPIvGQRyhWg7jK6mPSZqL8RRHc/PrAx+4uq4nTA==%27%2Cnecessary:true%2Cpreferences:false%2Cstatistics:false%2Cmarketing:false%2Cmethod:%27explicit%27%2Cver:1%2Cutc:1749402707581%2Cregion:%27us-06%27}; _fbp=fb.1.1749402732432.858927167364828607; _hjSession_5346215=eyJpZCI6IjM0MTgxNTY1LTJkNTUtNGQxZS1hOWU0LTczMjIwMWE0MGJkNiIsImMiOjE3NDk0MDI3MzM3MjIsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowLCJzcCI6MH0=; _clsk=1nn9fa9%7C1749402735035%7C10%7C1%7Ck.clarity.ms%2Fcollect',
        'priority': 'u=1, i'
      },
      body: formData
    });

    logInfo(logId, "LOGIN", "收到登录响应", {
      status: response.status,
      statusText: response.statusText,
      hasSetCookie: !!response.headers.get('set-cookie')
    });

    if (response.status === 202 || response.status === 302) {
      // 从 Set-Cookie 头中提取 auth token
      const setCookieHeader = response.headers.get('set-cookie');
      if (setCookieHeader) {
        const authTokenMatch = setCookieHeader.match(/sb-ppdjlmajmpcqpkdmnzfd-auth-token=([^;]+)/);
        if (authTokenMatch) {
          const authToken = authTokenMatch[1];
          logInfo(logId, "LOGIN", "成功提取认证令牌", {
            tokenPrefix: authToken.substring(0, 20) + "..."
          });
          return authToken;
        }
      }
    }

    logError(logId, "LOGIN", "登录失败：未找到认证令牌", {
      status: response.status,
      setCookie: response.headers.get('set-cookie')
    });
    return null;

  } catch (error) {
    logError(logId, "LOGIN", "登录过程发生异常", error);
    return null;
  }
}

async function extractVerticalAuthToken(authHeader: string | undefined, requestId?: string): Promise<string | null> {
  // 从 Authorization 头中提取 Vertical 令牌
  const logId = requestId || "UNKNOWN";

  if (!authHeader) return null;

  // 如果是完整的 cookie 字符串，直接返回
  if (authHeader.includes('sb-ppdjlmajmpcqpkdmnzfd-auth-token=') ||
      authHeader.includes('deviceId=') ||
      authHeader.includes('_clck=')) {
    logInfo(logId, "AUTH_TOKEN", "检测到完整cookie字符串");
    return authHeader;
  }

  // 支持 Bearer 格式: "Bearer token"
  const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/i);
  if (bearerMatch) {
    const token = bearerMatch[1];

    // 检查是否是用户名密码格式 (username---password)
    if (token.includes('---')) {
      logInfo(logId, "AUTH_TOKEN", "检测到用户名密码格式，开始登录");
      const [email, password] = token.split('---', 2);

      if (!email || !password) {
        logError(logId, "AUTH_TOKEN", "用户名密码格式无效", {
          hasEmail: !!email,
          hasPassword: !!password
        });
        return null;
      }

      // 执行登录获取令牌
      const authToken = await loginWithCredentials(email, password, logId);
      if (authToken) {
        logInfo(logId, "AUTH_TOKEN", "用户名密码登录成功");
        return authToken;
      } else {
        logError(logId, "AUTH_TOKEN", "用户名密码登录失败");
        return null;
      }
    }

    // 普通令牌
    logInfo(logId, "AUTH_TOKEN", "检测到普通Bearer令牌");
    return token;
  }

  // 直接返回令牌
  logInfo(logId, "AUTH_TOKEN", "直接使用提供的令牌");
  return authHeader;
}

// ===== 认证中间件 =====
function extractBearerToken(authHeader: string | undefined): string | null {
  if (!authHeader) return null;
  const match = authHeader.match(/^Bearer\s+(.+)$/i);
  return match ? match[1] : null;
}

async function authenticateClient(ctx: Context, next: () => Promise<unknown>): Promise<void> {
  const requestId = generateUUID().substring(0, 8);
  const authHeader = ctx.request.headers.get("Authorization");

  const token = extractBearerToken(authHeader);

  if (!token) {
    logError(requestId, "AUTH", "认证失败：缺少有效的Bearer token", {
      authHeader: authHeader ? authHeader.substring(0, 50) + "..." : null
    });
    ctx.response.status = 401;
    ctx.response.headers.set("WWW-Authenticate", "Bearer");
    ctx.response.body = { detail: "需要在 Authorization header 中提供 API 密钥" };
    return;
  }

  // 将 requestId 传递给后续处理
  (ctx as any).requestId = requestId;

  await next();
}

// ===== VerticalApiClient 实现 =====
class VerticalApiClient {
  constructor() {}

  async getChatId(modelUrl: string, authToken: string, requestId?: string): Promise<string | null> {
    const logId = requestId || "UNKNOWN";

    try {
      // 构造新的 URL 格式：添加 ?forceNewChat=true 参数
      const newChatUrl = `${modelUrl}?forceNewChat=true`;

      const response = await fetch(newChatUrl, {
        method: "GET",
        headers: {
          'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
          'Accept-Encoding': "gzip, deflate, br, zstd",
          'sentry-trace': "d06f91b597ee453f9e7c54d481037662-b6824dd09bb237be-1",
          'sec-ch-ua-platform': "\"Windows\"",
          'sec-ch-ua': "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\", \"Google Chrome\";v=\"134\"",
          'sec-ch-ua-mobile': "?0",
          'baggage': "sentry-environment=production,sentry-public_key=0fb63d9554f3efa0c9fa41330d426e6a,sentry-trace_id=d06f91b597ee453f9e7c54d481037662,sentry-sample_rate=1,sentry-sampled=true",
          'sec-fetch-site': "same-origin",
          'sec-fetch-mode': "cors",
          'sec-fetch-dest': "empty",
          'referer': "https://app.verticalstudio.ai/stream/models/gpt-4o/cmbnrdvwn0w8r9a0w1ea7d8yp",
          'accept-language': "zh-CN,zh;q=0.9",
          'priority': "u=1, i",
          'Cookie': "sb-ppdjlmajmpcqpkdmnzfd-auth-token="+authToken
        }
      });


      // 检查是否是重定向响应 (302)
      if (response.status === 302 || response.headers.get('location')) {
        const location = response.headers.get('location');
        if (location) {
          // 从重定向 URL 中提取 chatId
          // URL 格式: https://app.verticalstudio.ai/stream/models/gpt-4o/cmbnsberq0x4i9a0wpvkaoiv0
          const chatIdMatch = location.match(/\/([a-zA-Z0-9]+)$/);
          const chatId = chatIdMatch ? chatIdMatch[1] : null;

          return chatId;
        }
      }

      // 如果不是重定向，尝试解析响应体
      const responseText = await response.text();

      // 尝试从响应中提取 chatId
      // 响应格式可能包含类似 ["SingleFetchRedirect",1],{"_2":3,"_4":5,"_6":7,"_8":7,"_9":7},"redirect","https://app.verticalstudio.ai/stream/models/gpt-4o/cmbnsberq0x4i9a0wpvkaoiv0"
      const urlMatch = responseText.match(/https:\/\/app\.verticalstudio\.ai\/stream\/models\/[^\/]+\/([a-zA-Z0-9]+)/);
      const chatId = urlMatch ? urlMatch[1] : null;

      return chatId;
    } catch (error) {
      logError(logId, "CHAT_ID", "获取chat_id时发生异常", error);
      return null;
    }
  }

  async* sendMessageStream(
    authToken: string,
    chatId: string,
    message: string,
    modelId: string,
    outputReasoning: boolean,
    systemPrompt: string,
    requestId?: string
  ): AsyncGenerator<string, void, unknown> {
    const logId = requestId || "UNKNOWN";

    try {
      // 构造新的请求格式
      const messageId = generateUUID();
      const payload = {
        message: {
          id: messageId,
          createdAt: new Date().toISOString(),
          role: "user",
          content: message,
          parts: [{ type: "text", text: message }]
        },
        cornerType: "text",
        chatId: chatId,
        settings: {
          modelId: modelId,
          systemPromptPreset: systemPrompt ? "custom" : "default",
          toneOfVoice: "default"
        }
      };

      // 如果有自定义系统提示，添加到 settings 中
      if (systemPrompt) {
        (payload.settings as any).systemPrompt = systemPrompt;
      }


      // 使用新的 API 端点
      const apiUrl = "https://app.verticalstudio.ai/api/chat";

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "accept": "*/*",
          "accept-language": "zh-CN,zh;q=0.9",
          // "baggage": "sentry-environment=production,sentry-public_key=0fb63d9554f3efa0c9fa41330d426e6a,sentry-trace_id=d06f91b597ee453f9e7c54d481037662,sentry-sample_rate=1,sentry-sampled=true",
          "content-type": "application/json",
          "Cookie": "sb-ppdjlmajmpcqpkdmnzfd-auth-token="+authToken, // 使用完整的 cookie 字符串
          "origin": "https://app.verticalstudio.ai",
          "priority": "u=1, i",
          "referer": `https://app.verticalstudio.ai/stream/models/${modelId}/${chatId}`,
          "sec-ch-ua": '"Not:A-Brand";v="24", "Chromium";v="134", "Google Chrome";v="134"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          // "sentry-trace": "d06f91b597ee453f9e7c54d481037662-b6824dd09bb237be-1",
          "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        logError(logId, "STREAM", "流式请求失败", {
          status: response.status,
          statusText: response.statusText
        });
        yield `error:${JSON.stringify({ message: `HTTP ${response.status}: ${response.statusText}` })}`;
        return;
      }

      if (!response.body) {
        logError(logId, "STREAM", "响应体为空");
        yield `error:${JSON.stringify({ message: "No response body" })}`;
        return;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let chunkCount = 0;
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }

          chunkCount++;
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 按行分割处理
          const lines = buffer.split('\n');
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || "";

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine) {
              // 处理新的响应格式
              if (trimmedLine.startsWith('f:')) {
                // 消息ID信息，可以忽略或记录
              } else if (trimmedLine.match(/^\d+:/)) {
                // 内容行，格式如 0:"Hello"
                yield trimmedLine;
              } else if (trimmedLine.startsWith('e:')) {
                // 结束信息，包含使用统计
                yield `d:${trimmedLine.substring(2)}`; // 转换为旧格式
              } else if (trimmedLine.startsWith('d:')) {
                // 最终统计信息
                yield trimmedLine;
              } else {
                // 其他格式的行
                yield trimmedLine;
              }
            }
          }
        }

        // 处理缓冲区中剩余的内容
        if (buffer.trim()) {
          const trimmedLine = buffer.trim();
          if (trimmedLine.match(/^\d+:/)) {
            yield trimmedLine;
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      logError(logId, "STREAM", "流式处理发生异常", error);
      yield `error:${JSON.stringify({ message: `Network error: ${error.message}` })}`;
    }
  }
}

// ===== JSON 解析工具函数 =====
function parseJsonStringContent(line: string, prefixLen: number, suffixLen: number): string {
  const contentSegment = line.substring(prefixLen, line.length + suffixLen);
  try {
    // 将提取的片段视为JSON字符串的值进行解析，以处理所有标准转义序列
    return JSON.parse(`"${contentSegment}"`);
  } catch (error) {
    // 如果解析失败，回退到手动替换
    console.warn(`警告: parseJsonStringContent 中 JSONDecodeError，回退处理。原始片段: ${contentSegment.substring(0, 100)}...`);
    let tempContent = contentSegment.replace(/\\"/g, '"'); // 处理 \" -> "
    tempContent = tempContent.replace(/\\n/g, '\n');       // 处理 \n -> 换行符
    tempContent = tempContent.replace(/\\t/g, '\t');       // 处理 \t -> 制表符
    return tempContent;
  }
}

// ===== 对话缓存管理 =====
function updateConversationCache(
  isNewCachedConv: boolean,
  verticalChatIdForCache: string,
  matchedConvIdForCacheUpdate: string | null,
  originalRequestMessages: ChatMessage[],
  fullAssistantReplyStr: string,
  systemPromptHashForCache: number,
  modelUrlForCache: string
): void {
  if (isNewCachedConv) {
    // 新对话，创建缓存条目
    const newInternalId = generateUUID();
    const currentFingerprints: Promise<string>[] = originalRequestMessages.map(msg =>
      generateMessageFingerprint(msg.role, msg.content)
    );

    // 添加助手回复的指纹
    currentFingerprints.push(generateMessageFingerprint("assistant", fullAssistantReplyStr));

    Promise.all(currentFingerprints).then(fingerprints => {
      conversationCache.set(newInternalId, {
        vertical_chat_id: verticalChatIdForCache,
        vertical_model_url: modelUrlForCache,
        system_prompt_hash: systemPromptHashForCache,
        message_fingerprints: fingerprints,
        last_seen: Date.now()
      });

      // LRU 驱逐
      if (conversationCache.size > CONVERSATION_CACHE_MAX_SIZE) {
        const firstKey = conversationCache.keys().next().value;
        conversationCache.delete(firstKey);
      }
    });
  } else if (matchedConvIdForCacheUpdate) {
    // 更新现有对话
    const cachedItem = conversationCache.get(matchedConvIdForCacheUpdate);
    if (cachedItem) {
      // 添加最新用户消息的指纹（如果还未添加）
      if (originalRequestMessages.length > 0) {
        const lastUserMsg = originalRequestMessages[originalRequestMessages.length - 1];
        generateMessageFingerprint(lastUserMsg.role, lastUserMsg.content).then(lastUserFingerprint => {
          // 检查是否已经存在，避免重复
          if (!cachedItem.message_fingerprints ||
              cachedItem.message_fingerprints[cachedItem.message_fingerprints.length - 1] !== lastUserFingerprint) {
            cachedItem.message_fingerprints.push(lastUserFingerprint);
          }

          // 添加助手回复的指纹
          generateMessageFingerprint("assistant", fullAssistantReplyStr).then(assistantFingerprint => {
            cachedItem.message_fingerprints.push(assistantFingerprint);
            cachedItem.last_seen = Date.now();
          });
        });
      }
    }
  }
}

// ===== 流式响应适配器 =====
async function* openaiStreamAdapter(
  apiStreamGenerator: AsyncGenerator<string, void, unknown>,
  modelNameForResponse: string,
  reasoningRequested: boolean,
  verticalChatIdForCache: string,
  isNewCachedConv: boolean,
  matchedConvIdForCacheUpdate: string | null,
  originalRequestMessages: ChatMessage[],
  systemPromptHashForCache: number,
  modelUrlForCache: string,
  requestId?: string
): AsyncGenerator<string, void, unknown> {
  const logId = requestId || "UNKNOWN";
  const fullAssistantReplyParts: string[] = [];
  const streamId = `chatcmpl-${generateUUID().replace(/-/g, '')}`;

  logInfo(logId, "ADAPTER", "开始流式适配器处理", {
    streamId,
    modelName: modelNameForResponse,
    reasoningRequested,
    chatId: verticalChatIdForCache
  });

  try {
    let firstChunkSent = false;
    let lineCount = 0;

    for await (const line of apiStreamGenerator) {
      lineCount++;
      if (line.startsWith("error:")) {
        // 处理错误
        try {
          const errorData = JSON.parse(line.substring(6));
          const errorMsg = errorData.message || "Unknown error";

          const errorResp: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created: getCurrentTimestamp(),
            model: modelNameForResponse,
            choices: [{
              delta: { role: "assistant", content: `错误: ${errorMsg}` },
              index: 0,
              finish_reason: "stop"
            }]
          };
          yield `data: ${JSON.stringify(errorResp)}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        } catch {
          const errorMsg = "Unknown error from Vertical API";
          const errorResp: StreamResponse = {
            id: streamId,
            object: "chat.completion.chunk",
            created: getCurrentTimestamp(),
            model: modelNameForResponse,
            choices: [{
              delta: { role: "assistant", content: `错误: ${errorMsg}` },
              index: 0,
              finish_reason: "stop"
            }]
          };
          yield `data: ${JSON.stringify(errorResp)}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        }
      }

      let deltaPayload: Record<string, any> | null = null;

      // 解析新的 Vertical API 响应格式
      if (line.match(/^\d+:".*"$/)) {
        // 新格式的内容行，如 0:"Hello"
        const colonIndex = line.indexOf(':');
        const contentWithQuotes = line.substring(colonIndex + 1);
        const finalContent = parseJsonStringContent(contentWithQuotes, 1, -1);

        logInfo(logId, "ADAPTER", `处理主要内容 (第${lineCount}行)`, {
          contentLength: finalContent.length,
          contentPreview: finalContent.length > 100 ? finalContent.substring(0, 100) + "..." : finalContent,
          isFirstChunk: !firstChunkSent
        });

        if (!firstChunkSent) {
          deltaPayload = { role: "assistant", content: finalContent };
        } else {
          deltaPayload = { content: finalContent };
        }
        fullAssistantReplyParts.push(finalContent);
      } else if (reasoningRequested && line.startsWith('g:"') && line.endsWith('"')) {
        // 推理内容（仅当请求时才包含）
        const thinkingContent = parseJsonStringContent(line, 3, -1);
        logInfo(logId, "ADAPTER", `处理推理内容 (第${lineCount}行)`, {
          contentLength: thinkingContent.length,
          contentPreview: thinkingContent.length > 100 ? thinkingContent.substring(0, 100) + "..." : thinkingContent,
          isFirstChunk: !firstChunkSent
        });

        // 为缓存添加带前缀的思考内容，但在SSE事件中分离reasoning_content
        fullAssistantReplyParts.push(`[Thinking]: ${thinkingContent}`);
        if (!firstChunkSent) {
          deltaPayload = { role: "assistant", reasoning_content: thinkingContent };
        } else {
          deltaPayload = { reasoning_content: thinkingContent };
        }
      } else if (line.startsWith('d:')) {
        // 结束信号或统计信息
        logInfo(logId, "ADAPTER", `处理结束信号 (第${lineCount}行)`, { line });
        try {
          const eventData = JSON.parse(line.substring(2));
          if (eventData.finishReason === "stop" || eventData.type === "done" || eventData.type === "DONE") {
            logInfo(logId, "ADAPTER", "收到完成信号", { eventData });
            // 发送最终的 finish_reason
            const finalResp: StreamResponse = {
              id: streamId,
              object: "chat.completion.chunk",
              created: getCurrentTimestamp(),
              model: modelNameForResponse,
              choices: [{
                delta: {},
                index: 0,
                finish_reason: "stop"
              }]
            };
            yield `data: ${JSON.stringify(finalResp)}\n\n`;
            break;
          }
        } catch (parseError) {
          logWarning(logId, "ADAPTER", "解析结束信号失败", { line, error: parseError });
        }
      } else if (line.startsWith('e:')) {
        // 结束信息，包含使用统计
        logInfo(logId, "ADAPTER", `处理结束信息 (第${lineCount}行)`, { line });
        try {
          const eventData = JSON.parse(line.substring(2));
          if (eventData.finishReason === "stop") {
            logInfo(logId, "ADAPTER", "收到完成信号", { eventData });
            // 发送最终的 finish_reason
            const finalResp: StreamResponse = {
              id: streamId,
              object: "chat.completion.chunk",
              created: getCurrentTimestamp(),
              model: modelNameForResponse,
              choices: [{
                delta: {},
                index: 0,
                finish_reason: "stop"
              }]
            };
            yield `data: ${JSON.stringify(finalResp)}\n\n`;
            break;
          }
        } catch (parseError) {
          logWarning(logId, "ADAPTER", "解析结束信息失败", { line, error: parseError });
        }
      } else {
        // 未识别的行格式
        logWarning(logId, "ADAPTER", `未识别的行格式 (第${lineCount}行)`, {
          line: line.length > 200 ? line.substring(0, 200) + "..." : line,
          lineLength: line.length
        });
      }

      // 如果有内容要发送
      if (deltaPayload) {
        const streamResp: StreamResponse = {
          id: streamId,
          object: "chat.completion.chunk",
          created: getCurrentTimestamp(),
          model: modelNameForResponse,
          choices: [{
            delta: deltaPayload,
            index: 0
          }]
        };

        if (!firstChunkSent) {
          firstChunkSent = true;
        }

        yield `data: ${JSON.stringify(streamResp)}\n\n`;
      }
    }

    // 更新缓存
    const fullAssistantReply = fullAssistantReplyParts.join("\n");
    logInfo(logId, "ADAPTER", "更新对话缓存", {
      replyLength: fullAssistantReply.length,
      replyPartsCount: fullAssistantReplyParts.length,
      isNewConversation: isNewCachedConv,
      chatId: verticalChatIdForCache
    });

    updateConversationCache(
      isNewCachedConv,
      verticalChatIdForCache,
      matchedConvIdForCacheUpdate,
      originalRequestMessages,
      fullAssistantReply,
      systemPromptHashForCache,
      modelUrlForCache
    );

    // 发送结束标记
    logInfo(logId, "ADAPTER", "发送流式结束标记", { totalLines: lineCount });
    yield "data: [DONE]\n\n";

  } catch (error) {
    logError(logId, "ADAPTER", "流式适配器发生异常", error);
    const errorResp: StreamResponse = {
      id: streamId,
      object: "chat.completion.chunk",
      created: getCurrentTimestamp(),
      model: modelNameForResponse,
      choices: [{
        delta: { role: "assistant", content: `内部错误: ${error}` },
        index: 0,
        finish_reason: "stop"
      }]
    };
    yield `data: ${JSON.stringify(errorResp)}\n\n`;
    yield "data: [DONE]\n\n";
  }
}

// ===== 聚合流式响应用于非流式返回 =====
async function aggregateStreamForNonStreamResponse(
  openaiSseStream: AsyncGenerator<string, void, unknown>,
  modelName: string,
  requestId?: string
): Promise<ChatCompletionResponse> {
  const logId = requestId || "UNKNOWN";
  const contentParts: string[] = [];
  const reasoningParts: string[] = [];
  let lineCount = 0;

  logInfo(logId, "AGGREGATE", "开始聚合流式响应为非流式", { modelName });

  for await (const sseLine of openaiSseStream) {
    lineCount++;

    if (sseLine.startsWith("data: ") && sseLine !== "data: [DONE]\n\n") {
      try {
        const data = JSON.parse(sseLine.substring(6).trim());
        if (data.choices && data.choices.length > 0) {
          const delta = data.choices[0].delta || {};
          if (delta.content) {
            contentParts.push(delta.content);
            logInfo(logId, "AGGREGATE", `添加内容片段 (第${lineCount}行)`, {
              contentLength: delta.content.length,
              totalContentParts: contentParts.length
            });
          } else if (delta.reasoning_content) {
            reasoningParts.push(delta.reasoning_content);
            logInfo(logId, "AGGREGATE", `添加推理片段 (第${lineCount}行)`, {
              reasoningLength: delta.reasoning_content.length,
              totalReasoningParts: reasoningParts.length
            });
          }
        }
      } catch (parseError) {
        logWarning(logId, "AGGREGATE", `解析SSE行失败 (第${lineCount}行)`, {
          line: sseLine.length > 200 ? sseLine.substring(0, 200) + "..." : sseLine,
          error: parseError
        });
      }
    }
  }

  // 组合最终内容，如果有推理内容则添加
  const combinedParts: string[] = [];
  if (reasoningParts.length > 0) {
    logInfo(logId, "AGGREGATE", "添加推理内容到最终响应", {
      reasoningPartsCount: reasoningParts.length
    });
    for (const part of reasoningParts) {
      combinedParts.push(`[Thinking]: ${part}`);
    }
  }

  combinedParts.push(...contentParts);
  const fullContent = combinedParts.join("");

  logInfo(logId, "AGGREGATE", "聚合完成", {
    totalLines: lineCount,
    contentPartsCount: contentParts.length,
    reasoningPartsCount: reasoningParts.length,
    finalContentLength: fullContent.length,
    contentPreview: fullContent.length > 200 ? fullContent.substring(0, 200) + "..." : fullContent
  });

  const responseId = `chatcmpl-${generateUUID().replace(/-/g, '')}`;

  return {
    id: responseId,
    object: "chat.completion",
    created: getCurrentTimestamp(),
    model: modelName,
    choices: [{
      message: { role: "assistant", content: fullContent },
      index: 0,
      finish_reason: "stop"
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}

// ===== 路由设置 =====
const router = new Router();
const verticalApiClient = new VerticalApiClient();

// 模型列表端点
router.get("/v1/models", authenticateClient, (ctx: Context) => {
  const modelList: ModelInfo[] = [];
  for (const model of MODELS_DATA.data) {
    modelList.push({
      id: model.id || "",
      object: "model",
      created: model.created || getCurrentTimestamp(),
      owned_by: model.owned_by || "vertical-studio"
    });
  }

  ctx.response.body = {
    object: "list",
    data: modelList
  };
});

// 健康检查端点
router.get("/health", (ctx: Context) => {
  ctx.response.body = {
    status: "ok",
    service: "vertical-openai-adapter",
    models: MODELS_DATA.data.length,
    timestamp: getCurrentTimestamp()
  };
});

// 主要聊天完成端点
router.post("/v1/chat/completions", authenticateClient, async (ctx: Context) => {
  const requestId = (ctx as any).requestId || generateUUID().substring(0, 8);

  try {
    logInfo(requestId, "MAIN", "开始处理聊天完成请求");

    const request: ChatCompletionRequest = await ctx.request.body({ type: "json" }).value;

    logInfo(requestId, "MAIN", "解析请求体成功", {
      model: request.model,
      messagesCount: request.messages?.length || 0,
      stream: request.stream,
      temperature: request.temperature,
      max_tokens: request.max_tokens,
      top_p: request.top_p
    });

    // 获取模型配置
    const modelConfig = getModelItem(request.model);
    if (!modelConfig) {
      logError(requestId, "MAIN", "模型未找到", { requestedModel: request.model });
      ctx.response.status = 404;
      ctx.response.body = { detail: `模型 ${request.model} 未找到` };
      return;
    }

    logInfo(requestId, "MAIN", "找到模型配置", {
      modelId: modelConfig.id,
      verticalModelId: modelConfig.vertical_model_id,
      verticalModelUrl: modelConfig.vertical_model_url,
      outputReasoningFlag: modelConfig.output_reasoning_flag
    });

    // 获取 Authorization API 令牌
    let verticalTokenHeader = ctx.request.headers.get('authorization');
    const authToken = await extractVerticalAuthToken(verticalTokenHeader, requestId);
    if (!authToken) {
      logError(requestId, "MAIN", "Vertical API令牌提取失败", {
        verticalTokenHeader: verticalTokenHeader ? verticalTokenHeader.substring(0, 50) + "..." : null
      });
      ctx.response.status = 400;
      ctx.response.body = { detail: "需要在 Authorization 头中提供有效的认证信息（令牌或用户名---密码格式）" };
      return;
    }

    // 从模型配置中提取信息
    const verticalModelId = modelConfig.vertical_model_id;
    const verticalModelUrl = modelConfig.vertical_model_url;
    const outputReasoningActive = modelConfig.output_reasoning_flag || false;

    if (!verticalModelId || !verticalModelUrl) {
      logError(requestId, "CONFIG", "模型配置不完整", {
        hasVerticalModelId: !!verticalModelId,
        hasVerticalModelUrl: !!verticalModelUrl
      });
      ctx.response.status = 500;
      ctx.response.body = { detail: "模型配置不完整" };
      return;
    }

    // 提取系统提示和用户消息
    let currentSystemPromptStr = "";
    let latestUserMessageContent = "";
    const messageRoles: string[] = [];

    for (const msg of request.messages) {
      messageRoles.push(msg.role);
      if (msg.role === "system") {
        currentSystemPromptStr += msg.content + "\n";
      } else if (msg.role === "user") {
        latestUserMessageContent = msg.content;
      } else if (msg.role === "assistant") {
        logInfo(requestId, "MESSAGES", "处理助手消息", {
          contentLength: msg.content.length,
          contentPreview: msg.content.length > 100 ? msg.content.substring(0, 100) + "..." : msg.content
        });
      }
    }

    currentSystemPromptStr = currentSystemPromptStr.trim();

    logInfo(requestId, "MESSAGES", "消息处理完成", {
      messageRoles,
      systemPromptLength: currentSystemPromptStr.length,
      latestUserMessageLength: latestUserMessageContent.length,
      hasSystemPrompt: !!currentSystemPromptStr,
      hasUserMessage: !!latestUserMessageContent
    });

    if (!latestUserMessageContent) {
      logError(requestId, "MESSAGES", "未找到用户消息", {
        messageRoles,
        totalMessages: request.messages.length
      });
      ctx.response.status = 400;
      ctx.response.body = { detail: "请求中未找到用户消息" };
      return;
    }

    // 生成系统提示哈希
    logInfo(requestId, "CACHE", "生成系统提示哈希");
    const currentSystemPromptHash = await sha256Hash(currentSystemPromptStr).then(hash => hash.length);

    logInfo(requestId, "CACHE", "系统提示哈希生成完成", {
      hash: currentSystemPromptHash,
      systemPromptLength: currentSystemPromptStr.length
    });

    // 简化的缓存查找（在实际应用中可以实现更复杂的缓存逻辑）
    const isNewCachedConversation = true; // 简化实现
    const matchedConvId = null;

    logInfo(requestId, "CACHE", "缓存查找结果", {
      isNewCachedConversation,
      matchedConvId,
      cacheSize: conversationCache.size
    });

    // 新对话
    logInfo(requestId, "CHAT_ID", "开始创建新对话");
    const newChatId = await verticalApiClient.getChatId(verticalModelUrl, authToken, requestId);

    if (!newChatId) {
      logError(requestId, "CHAT_ID", "无法获取chat_id");
      ctx.response.status = 500;
      ctx.response.body = { detail: "无法从 Vertical API 获取 chat_id" };
      return;
    }

    logInfo(requestId, "CHAT_ID", "成功创建新对话", { chatId: newChatId });

    // 为新对话构造完整历史
    const historyParts: string[] = [];
    logInfo(requestId, "HISTORY", "开始构造消息历史");

    for (const msg of request.messages) {
      if (msg.role === "user") {
        historyParts.push(`User: ${msg.content}`);
      } else if (msg.role === "assistant") {
        historyParts.push(`Assistant: ${msg.content}`);
      }
    }

    const messageToSendToVertical = historyParts.length > 0 ? historyParts.join("\n") : latestUserMessageContent;

    logInfo(requestId, "HISTORY", "消息历史构造完成", {
      historyPartsCount: historyParts.length,
      finalMessageLength: messageToSendToVertical.length,
      messagePreview: messageToSendToVertical.length > 200 ? messageToSendToVertical.substring(0, 200) + "..." : messageToSendToVertical
    });

    // 调用 Vertical API
    logInfo(requestId, "API_CALL", "开始调用Vertical API", {
      chatId: newChatId,
      modelId: verticalModelId,
      outputReasoning: outputReasoningActive,
      messageLength: messageToSendToVertical.length,
      systemPromptLength: currentSystemPromptStr.length
    });

    const apiStreamGenerator = verticalApiClient.sendMessageStream(
      authToken,
      newChatId,
      messageToSendToVertical,
      verticalModelId,
      outputReasoningActive,
      currentSystemPromptStr,
      requestId
    );

    // 创建 OpenAI 格式的流
    logInfo(requestId, "ADAPTER", "创建OpenAI格式流适配器", {
      modelName: request.model,
      reasoningRequested: outputReasoningActive,
      isNewConversation: isNewCachedConversation
    });

    const openaiSseStream = openaiStreamAdapter(
      apiStreamGenerator,
      request.model,
      outputReasoningActive,
      newChatId,
      isNewCachedConversation,
      matchedConvId,
      request.messages,
      currentSystemPromptHash,
      verticalModelUrl,
      requestId
    );

    // 返回流式或非流式响应
    if (request.stream) {
      logInfo(requestId, "RESPONSE", "准备流式响应");

      ctx.response.headers.set("Content-Type", "text/event-stream");
      ctx.response.headers.set("Cache-Control", "no-cache");
      ctx.response.headers.set("Connection", "keep-alive");

      const body = new ReadableStream({
        async start(controller) {
          try {
            let chunkCount = 0;
            for await (const chunk of openaiSseStream) {
              chunkCount++;
              controller.enqueue(new TextEncoder().encode(chunk));
            }
            logInfo(requestId, "RESPONSE", "流式响应完成", { totalChunks: chunkCount });
          } catch (error) {
            logError(requestId, "RESPONSE", "流式响应错误", error);
          } finally {
            controller.close();
            logInfo(requestId, "RESPONSE", "流式响应控制器已关闭");
          }
        }
      });

      ctx.response.body = body;
    } else {
      logInfo(requestId, "RESPONSE", "准备非流式响应");
      const response = await aggregateStreamForNonStreamResponse(openaiSseStream, request.model, requestId);
      logInfo(requestId, "RESPONSE", "非流式响应生成完成", {
        responseId: response.id,
        choicesCount: response.choices.length,
        contentLength: response.choices[0]?.message?.content?.length || 0
      });
      ctx.response.body = response;
    }

    logInfo(requestId, "MAIN", "请求处理完成");

  } catch (error) {
    logError(requestId, "MAIN", "请求处理发生异常", error);
    ctx.response.status = 500;
    ctx.response.body = { detail: `内部服务器错误: ${error.message}` };
  }
});

// ===== 应用程序设置 =====
const app = new Application();

// 错误处理中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  try {
    await next();
  } catch (err) {
    console.error("Application error:", err);
    ctx.response.status = 500;
    ctx.response.body = { detail: "Internal server error" };
  }
});

// CORS 中间件
app.use(async (ctx: Context, next: () => Promise<unknown>) => {
  ctx.response.headers.set("Access-Control-Allow-Origin", "*");
  ctx.response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  ctx.response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Vertical-Token");

  if (ctx.request.method === "OPTIONS") {
    ctx.response.status = 200;
    return;
  }

  await next();
});

// 添加路由
app.use(router.routes());
app.use(router.allowedMethods());

// ===== 启动服务器 =====
async function main() {
  try {
    console.log("🚀 正在启动 Vertical OpenAI Compatible API 服务器...");
    console.log("📋 内置配置:");
    console.log(`   - 可用模型: ${MODELS_DATA.data.length} 个`);
    console.log("");
    console.log("🔗 API 端点:");
    console.log("  GET  /v1/models");
    console.log("  POST /v1/chat/completions");
    console.log("  GET  /health");
    console.log("");
    console.log("🔑 认证说明:");
    console.log("  模式1 - 直接使用令牌:");
    console.log("    - Authorization: Bearer <client-api-key>");
    console.log("    - X-Vertical-Token: <complete-cookies>");
    console.log("  模式2 - 用户名密码登录:");
    console.log("    - Authorization: Bearer <username>---<password>");
    console.log("");
    console.log(`🌐 服务器运行在 http://localhost:${PORT}`);
    console.log("Vertical OpenAI Compatible API 服务器已启动");

    await app.listen({ port: PORT });
  } catch (error) {
    console.error("❌ 启动服务器时出错:", error);
    // 使用标准退出而不是 Deno.exit
    throw error;
  }
}

// 启动服务器
main().catch(console.error);

{"total_models": 32, "providers": ["Anthropic", "Google", "Mistral AI", "OpenAI", "Unknown", "xAI", "Fal AI"], "provider_counts": {"Anthropic": 10, "Google": 6, "Mistral AI": 4, "OpenAI": 3, "Unknown": 1, "xAI": 4, "Fal AI": 4}, "models": [{"id": "claude-4-opus-20250514", "title": "Claude 4 Opus 20250514", "description": "Anthropic Advanced Language model: claude-4-opus-20250514", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "claude-4-opus", "title": "Claude 4 Opus", "description": "Anthropic Advanced Language model: claude-4-opus", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "claude-4-sonnet-20250514", "title": "Claude 4 Sonnet 20250514", "description": "Anthropic Language model: claude-4-sonnet-20250514", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-4-sonnet", "title": "Claude 4 Sonnet", "description": "Anthropic Language model: claude-4-sonnet", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-7-sonnet-20250219", "title": "Claude 3 7 Sonnet 20250219", "description": "Anthropic Language model: claude-3-7-sonnet-20250219", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-7-sonnet", "title": "Claude 3 7 Sonnet", "description": "Anthropic Language model: claude-3-7-sonnet", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-5-haiku-20241022", "title": "Claude 3 5 Haiku 20241022", "description": "Anthropic Language model: claude-3-5-haiku-20241022", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-5-haiku", "title": "Claude 3 5 Haiku", "description": "Anthropic Language model: claude-3-5-haiku", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-opus-20240229", "title": "Claude 3 Opus 20240229", "description": "Anthropic Advanced Language model: claude-3-opus-20240229", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "claude-3-opus", "title": "Claude 3 Opus", "description": "Anthropic Advanced Language model: claude-3-opus", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "gemini-2.5-flash-preview-04-17", "title": "Gemini 2.5 Flash Preview 04 17", "description": "Google Lightweight Language model: gemini-2.5-flash-preview-04-17", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "gemini-2-5-flash-preview-04-17", "title": "Gemini 2 5 Flash Preview 04 17", "description": "Google Lightweight Language model: gemini-2-5-flash-preview-04-17", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "gemini-2.5-pro-preview-05-06", "title": "Gemini 2.5 Pro Preview 05 06", "description": "Google Lightweight Language model: gemini-2.5-pro-preview-05-06", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "gemini-2-5-pro-preview-05-06", "title": "Gemini 2 5 Pro Preview 05 06", "description": "Google Lightweight Language model: gemini-2-5-pro-preview-05-06", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "mistral-large-latest", "title": "Mistral Large Latest", "description": "Mistral AI Advanced Language model: mistral-large-latest", "provider": "Mistral AI", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "mistral-large", "title": "Mistral Large", "description": "Mistral AI Advanced Language model: mistral-large", "provider": "Mistral AI", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "mistral-small-latest", "title": "Mistral Small Latest", "description": "Mistral AI Language model: mistral-small-latest", "provider": "Mistral AI", "type": "Language", "extracted_from": "reference_format"}, {"id": "mistral-small", "title": "Mistra<PERSON> Small", "description": "Mistral AI Language model: mistral-small", "provider": "Mistral AI", "type": "Language", "extracted_from": "reference_format"}, {"id": "gpt-4.1", "title": "GPT 4.1", "description": "OpenAI Language model: gpt-4.1", "provider": "OpenAI", "type": "Language", "extracted_from": "reference_format"}, {"id": "GPT-4.1", "title": "GPT 4.1", "description": "Unknown Language model: GPT-4.1", "provider": "Unknown", "type": "Language", "extracted_from": "reference_format"}, {"id": "gpt-4.1-mini", "title": "GPT 4.1 Mini", "description": "OpenAI Lightweight Language model: gpt-4.1-mini", "provider": "OpenAI", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "o4-mini", "title": "O4 Mini", "description": "OpenAI Lightweight Language model: o4-mini", "provider": "OpenAI", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "grok-3", "title": "Grok 3", "description": "xAI Language model: grok-3", "provider": "xAI", "type": "Language", "extracted_from": "reference_format"}, {"id": "grok-3-fast", "title": "Grok 3 Fast", "description": "xAI Language model: grok-3-fast", "provider": "xAI", "type": "Language", "extracted_from": "reference_format"}, {"id": "grok-3-mini", "title": "Grok 3 Mini", "description": "xAI Lightweight Language model: grok-3-mini", "provider": "xAI", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "fal-ai/flux/schnell", "title": "Fal-ai Flux Schnell", "description": "Fal AI Image Generation model: fal-ai/flux/schnell", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "flux-schnell", "title": "Flux Schnell", "description": "Fal AI Image Generation model: flux-schnell", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "fal-ai/flux/dev", "title": "Fal-ai Flux Dev", "description": "Fal AI Image Generation model: fal-ai/flux/dev", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "flux-dev", "title": "Flux Dev", "description": "Fal AI Image Generation model: flux-dev", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "grok-2-image", "title": "Grok 2 Image", "description": "xAI Image Generation model: grok-2-image", "provider": "xAI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "imagen-3.0-generate-002", "title": "Imagen 3.0 Generate 002", "description": "Google Image Generation model: imagen-3.0-generate-002", "provider": "Google", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "imagen-3.0-fast-generate-001", "title": "Imagen 3.0 Fast Generate 001", "description": "Google Image Generation model: imagen-3.0-fast-generate-001", "provider": "Google", "type": "Image Generation", "extracted_from": "reference_format"}], "models_by_provider": {"Anthropic": [{"id": "claude-4-opus-20250514", "title": "Claude 4 Opus 20250514", "description": "Anthropic Advanced Language model: claude-4-opus-20250514", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "claude-4-opus", "title": "Claude 4 Opus", "description": "Anthropic Advanced Language model: claude-4-opus", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "claude-4-sonnet-20250514", "title": "Claude 4 Sonnet 20250514", "description": "Anthropic Language model: claude-4-sonnet-20250514", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-4-sonnet", "title": "Claude 4 Sonnet", "description": "Anthropic Language model: claude-4-sonnet", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-7-sonnet-20250219", "title": "Claude 3 7 Sonnet 20250219", "description": "Anthropic Language model: claude-3-7-sonnet-20250219", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-7-sonnet", "title": "Claude 3 7 Sonnet", "description": "Anthropic Language model: claude-3-7-sonnet", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-5-haiku-20241022", "title": "Claude 3 5 Haiku 20241022", "description": "Anthropic Language model: claude-3-5-haiku-20241022", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-5-haiku", "title": "Claude 3 5 Haiku", "description": "Anthropic Language model: claude-3-5-haiku", "provider": "Anthropic", "type": "Language", "extracted_from": "reference_format"}, {"id": "claude-3-opus-20240229", "title": "Claude 3 Opus 20240229", "description": "Anthropic Advanced Language model: claude-3-opus-20240229", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "claude-3-opus", "title": "Claude 3 Opus", "description": "Anthropic Advanced Language model: claude-3-opus", "provider": "Anthropic", "type": "Advanced Language", "extracted_from": "reference_format"}], "Google": [{"id": "gemini-2.5-flash-preview-04-17", "title": "Gemini 2.5 Flash Preview 04 17", "description": "Google Lightweight Language model: gemini-2.5-flash-preview-04-17", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "gemini-2-5-flash-preview-04-17", "title": "Gemini 2 5 Flash Preview 04 17", "description": "Google Lightweight Language model: gemini-2-5-flash-preview-04-17", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "gemini-2.5-pro-preview-05-06", "title": "Gemini 2.5 Pro Preview 05 06", "description": "Google Lightweight Language model: gemini-2.5-pro-preview-05-06", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "gemini-2-5-pro-preview-05-06", "title": "Gemini 2 5 Pro Preview 05 06", "description": "Google Lightweight Language model: gemini-2-5-pro-preview-05-06", "provider": "Google", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "imagen-3.0-generate-002", "title": "Imagen 3.0 Generate 002", "description": "Google Image Generation model: imagen-3.0-generate-002", "provider": "Google", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "imagen-3.0-fast-generate-001", "title": "Imagen 3.0 Fast Generate 001", "description": "Google Image Generation model: imagen-3.0-fast-generate-001", "provider": "Google", "type": "Image Generation", "extracted_from": "reference_format"}], "Mistral AI": [{"id": "mistral-large-latest", "title": "Mistral Large Latest", "description": "Mistral AI Advanced Language model: mistral-large-latest", "provider": "Mistral AI", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "mistral-large", "title": "Mistral Large", "description": "Mistral AI Advanced Language model: mistral-large", "provider": "Mistral AI", "type": "Advanced Language", "extracted_from": "reference_format"}, {"id": "mistral-small-latest", "title": "Mistral Small Latest", "description": "Mistral AI Language model: mistral-small-latest", "provider": "Mistral AI", "type": "Language", "extracted_from": "reference_format"}, {"id": "mistral-small", "title": "Mistra<PERSON> Small", "description": "Mistral AI Language model: mistral-small", "provider": "Mistral AI", "type": "Language", "extracted_from": "reference_format"}], "OpenAI": [{"id": "gpt-4.1", "title": "GPT 4.1", "description": "OpenAI Language model: gpt-4.1", "provider": "OpenAI", "type": "Language", "extracted_from": "reference_format"}, {"id": "gpt-4.1-mini", "title": "GPT 4.1 Mini", "description": "OpenAI Lightweight Language model: gpt-4.1-mini", "provider": "OpenAI", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "o4-mini", "title": "O4 Mini", "description": "OpenAI Lightweight Language model: o4-mini", "provider": "OpenAI", "type": "Lightweight Language", "extracted_from": "reference_format"}], "Unknown": [{"id": "GPT-4.1", "title": "GPT 4.1", "description": "Unknown Language model: GPT-4.1", "provider": "Unknown", "type": "Language", "extracted_from": "reference_format"}], "xAI": [{"id": "grok-3", "title": "Grok 3", "description": "xAI Language model: grok-3", "provider": "xAI", "type": "Language", "extracted_from": "reference_format"}, {"id": "grok-3-fast", "title": "Grok 3 Fast", "description": "xAI Language model: grok-3-fast", "provider": "xAI", "type": "Language", "extracted_from": "reference_format"}, {"id": "grok-3-mini", "title": "Grok 3 Mini", "description": "xAI Lightweight Language model: grok-3-mini", "provider": "xAI", "type": "Lightweight Language", "extracted_from": "reference_format"}, {"id": "grok-2-image", "title": "Grok 2 Image", "description": "xAI Image Generation model: grok-2-image", "provider": "xAI", "type": "Image Generation", "extracted_from": "reference_format"}], "Fal AI": [{"id": "fal-ai/flux/schnell", "title": "Fal-ai Flux Schnell", "description": "Fal AI Image Generation model: fal-ai/flux/schnell", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "flux-schnell", "title": "Flux Schnell", "description": "Fal AI Image Generation model: flux-schnell", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "fal-ai/flux/dev", "title": "Fal-ai Flux Dev", "description": "Fal AI Image Generation model: fal-ai/flux/dev", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}, {"id": "flux-dev", "title": "Flux Dev", "description": "Fal AI Image Generation model: flux-dev", "provider": "Fal AI", "type": "Image Generation", "extracted_from": "reference_format"}]}}